from fastapi import APIRouter, Depends, Request
from ratelimit import limiter
from auth import verify_token
from services.prioritization_agent_service import PrioritizationAgent

prioritization_agent_router = APIRouter()


@prioritization_agent_router.get(
    "/v1/prioritization-agent", tags=["Features"]
)
@limiter.limit("5/minute")
async def prioritization_agent(
    request: Request,
    user: str = Depends(verify_token),
):
    """
    Endpoint to start the Prioritization Agent.
    
    The Prioritization Agent:
    1. Scans gs_patient_facility_referral_flag table for referrals with patient_to_facility_distance
       greater than the configured distance threshold in miles
    2. Updates the doc_status to "AI Status" for the latest version of matching referrals
       in gs_referral_decision table
    
    Args:
        request: The FastAPI request object.
        user: The authenticated user.
        
    Returns:
        Dict: Summary of the Prioritization Agent execution.
    """
    return await PrioritizationAgent().start(user)
