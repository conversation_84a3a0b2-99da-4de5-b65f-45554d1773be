import asyncio
import logging
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from db import get_db
from models.client_config import ClientConfiguration, ClientConfigurationParser
from exceptions import CustomError


class ClientConfigurationCache:
    """Simple in-memory cache for client configurations."""
    
    def __init__(self, ttl_minutes: int = 30):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._timestamps: Dict[str, datetime] = {}
        self._ttl = timedelta(minutes=ttl_minutes)
    
    def get(self, client_id: str) -> Optional[ClientConfiguration]:
        """Get cached configuration for a client."""
        if client_id not in self._cache:
            return None
        
        # Check if cache entry is expired
        if datetime.now() - self._timestamps[client_id] > self._ttl:
            self._remove(client_id)
            return None
        
        return ClientConfiguration.from_dict(self._cache[client_id])
    
    def set(self, client_id: str, config: ClientConfiguration) -> None:
        """Cache configuration for a client."""
        self._cache[client_id] = config.to_dict()
        self._timestamps[client_id] = datetime.now()
    
    def _remove(self, client_id: str) -> None:
        """Remove cached entry for a client."""
        self._cache.pop(client_id, None)
        self._timestamps.pop(client_id, None)
    
    def clear(self) -> None:
        """Clear all cached entries."""
        self._cache.clear()
        self._timestamps.clear()
    
    def invalidate(self, client_id: str) -> None:
        """Invalidate cache for a specific client."""
        self._remove(client_id)


class ClientConfigurationService:
    """Service to manage client-specific configurations."""
    
    def __init__(self, cache_ttl_minutes: int = 30):
        self._cache = ClientConfigurationCache(cache_ttl_minutes)
        self._parser = ClientConfigurationParser()
        self._default_config = ClientConfiguration()
    
    async def get_client_configuration(self, client_id: str, client_schema: Optional[str] = None) -> ClientConfiguration:
        """
        Get configuration for a specific client.
        
        Args:
            client_id: The client identifier
            client_schema: Optional client schema name
            
        Returns:
            ClientConfiguration object with merged settings
        """
        # Check cache first
        cached_config = self._cache.get(client_id)
        if cached_config is not None:
            logging.debug(f"Using cached configuration for client: {client_id}")
            return cached_config
        
        try:
            # Fetch from database
            client_config_data = await self._fetch_client_config_from_db(client_id, client_schema)
            
            # Parse and merge with defaults
            if client_config_data:
                parsed_config = self._parser.parse_xml_config(client_config_data)
                # Add client identification
                parsed_config['client_id'] = client_id
                parsed_config['client_schema'] = client_schema or client_id
                merged_config = self._parser.merge_with_defaults(parsed_config, self._default_config)
            else:
                # Use defaults with client identification
                merged_config = ClientConfiguration(
                    client_id=client_id,
                    client_schema=client_schema or client_id
                )
            
            # Cache the result
            self._cache.set(client_id, merged_config)
            logging.info(f"Loaded and cached configuration for client: {client_id}")
            
            return merged_config
            
        except Exception as e:
            logging.error(f"Error loading configuration for client {client_id}: {e}")
            # Return default configuration with client identification
            return ClientConfiguration(
                client_id=client_id,
                client_schema=client_schema or client_id
            )
    
    async def _fetch_client_config_from_db(self, client_id: str, client_schema: Optional[str] = None) -> Optional[str]:
        """
        Fetch client configuration XML from database.
        
        Args:
            client_id: The client identifier
            client_schema: Optional client schema name
            
        Returns:
            XML configuration string or None if not found
        """
        try:
            async with get_db() as db:
                # Try to fetch from client-specific configuration table
                # Assuming there's a table like ld_admin.client_configurations
                query = text("""
                    SELECT config_xml 
                    FROM ld_admin.client_configurations 
                    WHERE client_id = :client_id 
                    AND is_active = true
                    ORDER BY updated_at DESC 
                    LIMIT 1
                """)
                
                result = await db.execute(query, {"client_id": client_id})
                row = result.fetchone()
                
                if row and row[0]:
                    return row[0]
                
                # If no specific configuration found, try to get from client schema
                if client_schema:
                    schema_query = text(f"""
                        SELECT config_xml 
                        FROM {client_schema}.ai_configuration 
                        WHERE is_active = true
                        ORDER BY updated_at DESC 
                        LIMIT 1
                    """)
                    
                    result = await db.execute(schema_query)
                    row = result.fetchone()
                    
                    if row and row[0]:
                        return row[0]
                
                return None
                
        except SQLAlchemyError as e:
            logging.error(f"Database error fetching config for client {client_id}: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error fetching config for client {client_id}: {e}")
            return None
    
    async def refresh_client_configuration(self, client_id: str, client_schema: Optional[str] = None) -> ClientConfiguration:
        """
        Force refresh of client configuration from database.
        
        Args:
            client_id: The client identifier
            client_schema: Optional client schema name
            
        Returns:
            Refreshed ClientConfiguration object
        """
        # Invalidate cache
        self._cache.invalidate(client_id)
        
        # Fetch fresh configuration
        return await self.get_client_configuration(client_id, client_schema)
    
    def get_default_configuration(self) -> ClientConfiguration:
        """Get the default configuration."""
        return self._default_config
    
    async def validate_client_exists(self, client_id: str) -> bool:
        """
        Validate that a client exists in the system.
        
        Args:
            client_id: The client identifier
            
        Returns:
            True if client exists, False otherwise
        """
        try:
            async with get_db() as db:
                query = text("""
                    SELECT 1 
                    FROM ld_admin.ld_clients 
                    WHERE client_id = :client_id
                    LIMIT 1
                """)
                
                result = await db.execute(query, {"client_id": client_id})
                return result.fetchone() is not None
                
        except Exception as e:
            logging.error(f"Error validating client {client_id}: {e}")
            return False
    
    async def get_client_schema(self, client_id: str) -> Optional[str]:
        """
        Get the database schema name for a client.
        
        Args:
            client_id: The client identifier
            
        Returns:
            Schema name or None if not found
        """
        try:
            async with get_db() as db:
                query = text("""
                    SELECT schema_name 
                    FROM ld_admin.ld_clients 
                    WHERE client_id = :client_id
                    LIMIT 1
                """)
                
                result = await db.execute(query, {"client_id": client_id})
                row = result.fetchone()
                
                return row[0] if row else None
                
        except Exception as e:
            logging.error(f"Error getting schema for client {client_id}: {e}")
            return None


# Global service instance
_client_config_service: Optional[ClientConfigurationService] = None


def get_client_config_service() -> ClientConfigurationService:
    """Get the global client configuration service instance."""
    global _client_config_service
    if _client_config_service is None:
        _client_config_service = ClientConfigurationService()
    return _client_config_service
