import asyncio
import logging
from datetime import datetime, timezone
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, START, END
from fastapi import Request
from db import get_db
from db.monitoring import update_agent_run_timestamp
from db.missing_info_agent_operations import get_clients
from db.prioritization_agent_operations import (
    get_flagged_referrals_by_decline_rules,
    update_referral_status_based_on_scores_and_distance,
)
from utils.config_utils import get_config_from_request
from services.client_config_service import get_client_config_service


class PrioritizationAgentState(TypedDict):
    """State for the Prioritization Agent workflow."""

    client_schema: str
    referrals_to_process: List[Dict[str, Any]]
    processed_referrals: List[Dict[str, Any]]
    summary: Dict[str, Any]


class PrioritizationAgent:
    """
    Prioritization Agent that processes referrals based on patient-to-facility distance
    and updates referral decision status accordingly.
    """

    def __init__(self, request: Request = None):
        self.request = request
        self.workflow_enabled = False
        self.graph = StateGraph(PrioritizationAgentState)

        # Get configuration (client-specific or default)
        if request:
            config = get_config_from_request(request)
            self.disable_prioritization_agent = config.disable_prioritization_agent
            self.disable_auto_decline_workflow = config.disable_auto_decline_workflow
        else:
            # Fallback to default configuration for scheduler
            default_config = get_client_config_service().get_default_configuration()
            self.disable_prioritization_agent = (
                default_config.disable_prioritization_agent
            )
            self.disable_auto_decline_workflow = (
                default_config.disable_auto_decline_workflow
            )

        self.create_auto_decline_workflow()

    def create_auto_decline_workflow(self):
        """Creates the auto decline workflow."""
        if self.disable_auto_decline_workflow == "true":
            logging.info("Auto decline workflow is disabled. Skipping it...")
            return

        self.graph.add_node(
            "Identify Low Score and High Distance Referrals",
            self.identify_low_score_and_high_distance_referrals,
        )
        self.graph.add_node("Update Referral AI Status", self.update_referral_ai_status)
        self.graph.add_node(
            "Summarize Prioritization Agent Actions", self.summarize_agent_actions
        )
        self.graph.add_edge(START, "Identify Low Score and High Distance Referrals")
        self.graph.add_edge(
            "Identify Low Score and High Distance Referrals",
            "Update Referral AI Status",
        )
        self.graph.add_edge(
            "Update Referral AI Status",
            "Summarize Prioritization Agent Actions",
        )
        self.graph.add_edge("Summarize Prioritization Agent Actions", END)

        self.workflow_enabled = True

    async def execute_workflow(self, state: PrioritizationAgentState):
        """Executes the workflow for a given client schema."""
        return await self.graph.compile(debug=False).ainvoke(state)

    async def run(self, clients: List[str]):
        logging.info(f"Identified {len(clients)} client(s).")
        tasks = []
        for client in clients:
            state = PrioritizationAgentState(
                client_schema=f"{client}_referral",
                referrals_to_process=[],
                processed_referrals=[],
                summary={},
            )
            tasks.append(self.execute_workflow(state))

        await asyncio.gather(*tasks)
        logging.info("Workflow completed.")

    async def identify_low_score_and_high_distance_referrals(
        self, state: PrioritizationAgentState
    ) -> PrioritizationAgentState:
        """
        Identify referrals where score is low and patient-to-facility distance exceeds the configured threshold.
        """
        client_schema = state["client_schema"]

        logging.info(
            f"Starting identification of low score referrals for client: {client_schema}"
        )

        async with get_db() as db:
            referrals_to_process = await get_flagged_referrals_by_decline_rules(
                client_schema, db, self.request
            )
        state["referrals_to_process"] = referrals_to_process
        return state

    async def update_referral_ai_status(
        self, state: PrioritizationAgentState
    ) -> PrioritizationAgentState:
        """
        Update the doc_status to "AI Status" for referrals exceeding distance threshold.
        """
        client_schema = state["client_schema"]
        referrals_to_process = state["referrals_to_process"]
        processed_referrals = []

        logging.info(
            f"Starting update of referral decision status for {len(referrals_to_process)} referrals"
        )

        if not referrals_to_process:
            logging.info("No referrals to process for status update.")
            state["processed_referrals"] = processed_referrals
            return state

        async with get_db() as db:
            for referral in referrals_to_process:
                refer_id = referral["refer_id"]
                result = await update_referral_status_based_on_scores_and_distance(
                    referral, client_schema, db, self.request
                )
                processed_referrals.append(
                    {
                        "refer_id": refer_id,
                        "result": result,
                        "distance_info": referral.get("refer_dcsn_priority_json", {})
                        .get("score", {})
                        .get("patient_to_facility_distance", {}),
                    }
                )

                logging.info(
                    f"Processed referral {refer_id}: {result.get('status', 'UNKNOWN')}"
                )

        logging.info(
            f"Completed processing {len(processed_referrals)} referrals for status update"
        )

        state["processed_referrals"] = processed_referrals
        return state

    async def summarize_agent_actions(
        self, state: PrioritizationAgentState
    ) -> PrioritizationAgentState:
        """
        Summarize the actions taken by the Prioritization Agent.
        """
        client_schema = state["client_schema"]
        referrals_to_process = state["referrals_to_process"]
        processed_referrals = state["processed_referrals"]

        successful_updates = len(
            [r for r in processed_referrals if r["result"]["status"] == "SUCCESS"]
        )
        no_updates = len(
            [r for r in processed_referrals if r["result"]["status"] == "NO_UPDATE"]
        )

        summary = {
            "client_schema": client_schema,
            "total_referrals_identified": len(referrals_to_process),
            "total_referrals_processed": len(processed_referrals),
            "successful_updates": successful_updates,
            "no_updates": no_updates,
        }

        logging.info(
            f"Prioritization Agent Summary for {client_schema}: "
            f"Identified: {summary['total_referrals_identified']}, "
            f"Processed: {summary['total_referrals_processed']}, "
            f"Successful: {successful_updates}, "
            f"No Updates: {no_updates}"
        )

        state["summary"] = summary
        return state

    async def start(self, user: str = "Scheduler") -> Dict[str, Any]:
        """
        Start the Prioritization Agent workflow for all clients.

        Args:
            user: The user who initiated the agent.

        Returns:
            Dict[str, Any]: Summary of the agent execution.
        """
        logging.info(f"Starting Prioritization Agent initiated by user: {user}")
        if self.disable_prioritization_agent == "true":
            return {
                "message": "Prioritization Agent is disabled. Please enable it to run."
            }
        if not self.workflow_enabled:
            logging.info("No workflow is enabled for prioritization agent. Skipping.")
            return {
                "message": "No workflow is enabled for prioritization agent to run."
            }

        clients = await get_clients()
        current_time = datetime.now(timezone.utc)
        await self.run(clients)

        async with get_db() as db:
            for client in clients:
                schema_name = f"{client}_referral"
                # Update agent run timestamp
                await update_agent_run_timestamp(
                    db=db,
                    agent_name="Prioritization Agent",
                    schema_name=schema_name,
                    run_timestamp=current_time,
                )

        return {"message": f"Prioritization Agent completed successfully by {user}."}
