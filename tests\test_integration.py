import pytest
import async<PERSON>
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
from fastapi.testclient import TestClient

from middleware.middlewares import load_client_configuration
from utils.config_utils import get_config_from_request, get_missing_fields_to_look
from models.client_config import ClientConfiguration


class TestClientConfigurationIntegration:
    """Integration tests for client configuration system."""
    
    @pytest.fixture
    def app(self):
        """Create a test FastAPI app with middleware."""
        app = FastAPI()
        
        @app.get("/test")
        async def test_endpoint(request: Request):
            """Test endpoint that uses client configuration."""
            config = get_config_from_request(request)
            missing_fields = get_missing_fields_to_look(request)
            
            return {
                "client_id": config.client_id,
                "client_schema": config.client_schema,
                "root_path": config.root_path,
                "missing_fields": missing_fields
            }
        
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create a test client."""
        return TestClient(app)
    
    def test_middleware_loads_default_config(self, app, client):
        """Test that middleware loads default configuration when no client is identified."""
        
        @app.middleware("http")
        async def test_middleware(request: Request, call_next):
            await load_client_configuration(request)
            response = await call_next(request)
            return response
        
        with patch('middleware.middlewares.ClientIdentificationService') as mock_service_class:
            mock_service = mock_service_class.return_value
            mock_service.identify_client_from_request = AsyncMock(return_value=(None, None))
            
            with patch('middleware.middlewares.ClientConfigurationService') as mock_config_service_class:
                mock_config_service = mock_config_service_class.return_value
                mock_config_service.get_default_configuration.return_value = ClientConfiguration()
                
                response = client.get("/test")
        
        assert response.status_code == 200
        data = response.json()
        assert data["client_id"] is None
        assert data["root_path"] == "/referral-ai"  # default value
    
    def test_middleware_loads_client_specific_config(self, app, client):
        """Test that middleware loads client-specific configuration."""
        
        @app.middleware("http")
        async def test_middleware(request: Request, call_next):
            await load_client_configuration(request)
            response = await call_next(request)
            return response
        
        # Mock client configuration
        client_config = ClientConfiguration(
            client_id="test_client",
            client_schema="test_schema",
            root_path="/custom-path",
            missing_fields_to_look=["custom_field1", "custom_field2"]
        )
        
        with patch('middleware.middlewares.ClientIdentificationService') as mock_service_class:
            mock_service = mock_service_class.return_value
            mock_service.identify_client_from_request = AsyncMock(return_value=("test_client", "test_schema"))
            
            with patch('middleware.middlewares.ClientConfigurationService') as mock_config_service_class:
                mock_config_service = mock_config_service_class.return_value
                mock_config_service.get_client_configuration = AsyncMock(return_value=client_config)
                
                response = client.get("/test", headers={"X-Client-ID": "test_client"})
        
        assert response.status_code == 200
        data = response.json()
        assert data["client_id"] == "test_client"
        assert data["client_schema"] == "test_schema"
        assert data["root_path"] == "/custom-path"
        assert data["missing_fields"] == ["custom_field1", "custom_field2"]
    
    def test_config_endpoint_uses_client_config(self, app, client):
        """Test that the config endpoint returns client-specific configuration."""
        from routes.config import config_router
        app.include_router(config_router)
        
        @app.middleware("http")
        async def test_middleware(request: Request, call_next):
            await load_client_configuration(request)
            response = await call_next(request)
            return response
        
        # Mock client configuration
        client_config = ClientConfiguration(
            client_id="test_client",
            client_schema="test_schema",
            root_path="/custom-path",
            server_name="custom-server",
            encryption_algorithm="HS512",
            access_token_expire_minutes=120,
            missing_fields_to_look=["ssn", "address"],
            fields_mapping_prompt=["Social Security Number", "Patient Address"],
            missing_fields_labels=["SSN", "Address"],
            high_priority_fields=["ssn"]
        )
        
        with patch('middleware.middlewares.ClientIdentificationService') as mock_service_class:
            mock_service = mock_service_class.return_value
            mock_service.identify_client_from_request = AsyncMock(return_value=("test_client", "test_schema"))
            
            with patch('middleware.middlewares.ClientConfigurationService') as mock_config_service_class:
                mock_config_service = mock_config_service_class.return_value
                mock_config_service.get_client_configuration = AsyncMock(return_value=client_config)
                
                response = client.get("/v1/config", headers={"X-Client-ID": "test_client"})
        
        assert response.status_code == 200
        xml_content = response.content.decode()
        
        # Verify client-specific values are in the XML
        assert "/custom-path" in xml_content
        assert "custom-server" in xml_content
        assert "HS512" in xml_content
        assert "120" in xml_content
        assert "ssn" in xml_content
        assert "Social Security Number" in xml_content
    
    def test_error_handling_fallback_to_defaults(self, app, client):
        """Test that errors in client config loading fall back to defaults."""
        
        @app.middleware("http")
        async def test_middleware(request: Request, call_next):
            await load_client_configuration(request)
            response = await call_next(request)
            return response
        
        with patch('middleware.middlewares.ClientIdentificationService') as mock_service_class:
            mock_service = mock_service_class.return_value
            # Simulate an error in client identification
            mock_service.identify_client_from_request = AsyncMock(side_effect=Exception("Database error"))
            
            with patch('middleware.middlewares.ClientConfigurationService') as mock_config_service_class:
                mock_config_service = mock_config_service_class.return_value
                mock_config_service.get_default_configuration.return_value = ClientConfiguration()
                
                response = client.get("/test")
        
        assert response.status_code == 200
        data = response.json()
        assert data["client_id"] is None
        assert data["root_path"] == "/referral-ai"  # default value


class TestAuthenticationIntegration:
    """Integration tests for authentication with client information."""
    
    def test_jwt_token_includes_client_info(self):
        """Test that JWT tokens include client information."""
        import jwt
        from auth import create_access_token
        
        # Test token creation with client info
        token_data = {"sub": "test_user"}
        
        async def test_token_creation():
            token = await create_access_token(
                token_data, 
                client_id="test_client", 
                client_schema="test_schema"
            )
            
            # Decode token to verify client info is included
            decoded = jwt.decode(token, options={"verify_signature": False})
            
            assert decoded["sub"] == "test_user"
            assert decoded["client_id"] == "test_client"
            assert decoded["client_schema"] == "test_schema"
            assert decoded["type"] == "access"
        
        asyncio.run(test_token_creation())


if __name__ == "__main__":
    pytest.main([__file__])
