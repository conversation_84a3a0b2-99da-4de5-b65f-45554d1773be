from fastapi import <PERSON><PERSON><PERSON>, status
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTT<PERSON>Exception as StarletteHTTPException
from slowapi import _rate_limit_exceeded_handler
from slowapi.middleware import SlowAPIMiddleware
from services.client_config_service import get_client_config_service
from middleware.middlewares import http_middleware
from ratelimit import limiter
from helpers.version_helper import get_version
from handlers.exception_handler import (
    all_exception_handler,
    all_http_exception_handler,
    request_validation_exception_handler,
)
from routes import all_routers
from scheduler.scheduler_manager import lifespan

# Get default configuration for application startup
_default_config = get_client_config_service().get_default_configuration()

app = FastAPI(
    root_path=_default_config.root_path,
    title=f"Referral AI {get_version()}",
    description="Referral AI - Application to handle AI for LincWare!",
    swagger_ui_parameters={"syntaxHighlight": False, "displayRequestDuration": True},
    version=get_version(),
    openapi_tags=[
        {"name": "Auth", "description": "Authentication endpoints"},
        {"name": "Features", "description": "Secure feature endpoints"},
        {"name": "Health Check", "description": "Health check endpoints"},
        {"name": "Monitoring", "description": "Monitoring endpoints"},
        {"name": "Configuration", "description": "Configuration endpoints"},
    ],
    docs_url=None,
    openapi_url=None,
    lifespan=lifespan,
)

app.middleware("http")(http_middleware)
app.add_middleware(SlowAPIMiddleware)

app.add_exception_handler(Exception, all_exception_handler)
app.add_exception_handler(StarletteHTTPException, all_http_exception_handler)
app.add_exception_handler(RequestValidationError, request_validation_exception_handler)
app.add_exception_handler(
    status.HTTP_429_TOO_MANY_REQUESTS, _rate_limit_exceeded_handler
)

app.state.limiter = limiter

for route in all_routers:
    app.include_router(route)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=9000)
