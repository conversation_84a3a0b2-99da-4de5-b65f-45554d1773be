from fastapi import status, Request
from utils.db_utils import get_model
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, select, update
from sqlalchemy.exc import SQLAlchemyError
from exceptions import CustomError
from typing import List, Dict, Any, Callable, TypeVar, Awaitable, Optional
from datetime import datetime, timedelta, timezone
from models import PatientReferralAI, ReferralComment, ReferralTask
from utils.fields_mapping import map_missing_field_to_labels
from utils.config_utils import get_config_from_request
from services.client_config_service import get_client_config_service

T = TypeVar("T")


async def get_referrals_with_pending_tasks(
    schema_name: str, db: AsyncSession, request: Optional[Request] = None
) -> List[Dict[str, Any]]:
    """
    Get all rows from the gs_patient_referral_ai table where `require_task` is True
    and the `created_at` timestamp is older than the threshold time using SQLAlchemy.

    Args:
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.
        request: FastAPI request object for client configuration

    Returns:
        List[Dict[str, Any]]: List of rows that meet the criteria.
    """
    # Get configuration (client-specific or default)
    if request:
        config = get_config_from_request(request)
    else:
        # Fallback to default configuration
        config = get_client_config_service().get_default_configuration()

    try:
        threshold_time = datetime.now(timezone.utc) - timedelta(
            minutes=config.task_creation_waiting_minutes
        )

        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        query = select(
            PatientReferralAIModel.refer_id,
            PatientReferralAIModel.patient_id,
            PatientReferralAIModel.res_fields_json,
            PatientReferralAIModel.created_at,
            PatientReferralAIModel.require_task,
            PatientReferralAIModel.task_created,
        ).where(
            and_(
                PatientReferralAIModel.require_task == True,
                PatientReferralAIModel.task_created == False,
                PatientReferralAIModel.created_at < threshold_time,
            )
        )

        pending_tasks_records = await db.execute(query)
        rows = pending_tasks_records.fetchall()
        columns = pending_tasks_records.keys()
        referrals_with_pending_tasks = [dict(zip(columns, row)) for row in rows]
        logging.info(
            f"Retrieved {len(referrals_with_pending_tasks)} referrals with pending tasks."
        )
        return referrals_with_pending_tasks

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def execute_in_transaction(
    operation: Callable[[AsyncSession, Any], Awaitable[T]],
    *args,
    db: AsyncSession,
    **kwargs,
) -> T:
    """
    Executes a series of database operations within a single transaction using SQLAlchemy.

    Args:
        operation: A callable that performs the database operations.
        *args: Positional arguments to pass to the operation.
        db: SQLAlchemy async database session.
        **kwargs: Keyword arguments to pass to the operation.

    Returns:
        Any: The result of the operation.
    """
    try:
        async with db.begin():
            task_creation_result = await operation(db, *args, **kwargs)
            return task_creation_result
    except SQLAlchemyError as e:
        logging.info("Reverted the task creation transaction.")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.info("Reverted the task creation transaction.")
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def create_task_for_single_referral(
    db: AsyncSession,
    refer_id: int,
    patient_id: int,
    client_schema: str,
    priority: str,
    created_task_message: str,
    modified_task_message: str,
    task_status: str = NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS,
    comment_status: str = NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS,
) -> Dict[str, Any]:
    """
    Creates a task for a single referral using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        refer_id: Referral ID.
        patient_id: Patient ID.
        res_fields_json: JSON containing the referral fields.
        client_schema: The schema name for the database.
        priority: Priority level.
        created_task_message: Message for the created task.
        modified_task_message: Message for the modified task.
        task_status: Status of the task.
        comment_status: Status of the comment.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    referral_comment = await add_referral_comment_in_db(
        db=db,
        patient_id=patient_id,
        refer_id=refer_id,
        priority=priority,
        schema_name=client_schema,
        comment=created_task_message,
        comment_status=comment_status,
    )
    referral_comment_id = referral_comment["comment_id"]
    task_response = await create_task_for_missing_info(
        db=db,
        cmt_id=referral_comment_id,
        patient_id=patient_id,
        refer_id=refer_id,
        priority=priority,
        schema_name=client_schema,
        created_task_message=created_task_message,
        modified_task_message=modified_task_message,
        task_status=task_status,
    )
    await update_created_task_status(
        db=db, refer_id=refer_id, schema_name=client_schema
    )
    return task_response


async def create_tasks_for_missing_fields(
    task_list: List[Dict[str, Any]],
    client_schema: str,
    db: AsyncSession,
) -> List[Dict[str, Any]]:
    """
    Creates tasks for missing fields in a using SQLAlchemy.

    Args:
        task_list: List of tasks to process.
        client_schema: The schema name for the database.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: Summary of created tasks.
    """

    logging.info(f"Tasks to create: {len(task_list)}")
    agent_summary = []

    for task in task_list:
        refer_id = task["refer_id"]
        patient_id = task["patient_id"]
        res_fields_json = task["res_fields_json"]

        missing_data_points = []
        for key, value in res_fields_json.items():
            if value is None or value == "":
                missing_data_points.append(key)

        if not missing_data_points:
            logging.info(f"No missing data points for refer_id={refer_id}. Skipping.")
            continue

        priority = (
            "PD"
            if not set(missing_data_points).isdisjoint(set(HIGH_PRIORITY_FIELDS))
            else "N"
        )
        missing_data_points = map_missing_field_to_labels(missing_data_points)
        data_point = ", ".join(missing_data_points)

        task_response = await create_task_for_single_referral(
            db=db,
            refer_id=refer_id,
            patient_id=patient_id,
            client_schema=client_schema,
            priority=priority,
            created_task_message=COMMENT.format(data_point=data_point),
            modified_task_message=COMMENT.format(data_point=data_point),
        )

        agent_summary.append(task_response)

    logging.info("Task creation transaction completed.")
    return agent_summary


async def add_referral_comment_in_db(
    db: AsyncSession,
    patient_id: str,
    refer_id: str,
    priority: str,
    schema_name: str,
    comment: str,
    comment_status: str = NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS,
    parent_id: int = None,
) -> Dict[str, Any]:
    """
    Add a referral comment to the database using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        patient_id: Patient ID.
        refer_id: Referral ID.
        priority: Priority level.
        schema_name: Schema name.
        comment: Comment message.
        comment_status: Status of the comment.
        parent_id: Optional parent comment ID for threaded comments.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        logging.info(f"Creating comment for refer_id: {refer_id}.")
        ReferralCommentModel = await get_model(ReferralComment, schema_name)
        current_time = datetime.now(timezone.utc)
        new_comment = ReferralCommentModel(
            patient_id=patient_id,
            refer_id=refer_id,
            created_date=current_time,
            created_by=CREATED_BY_USER_ID,
            created=CREATED_BY_USERNAME,
            cmt_type=COMMENT_TYPE,
            subject=TASK_SUBJECT,
            status=comment_status,
            priority=priority,
            cmt=comment,
            parent_id=parent_id,
        )

        db.add(new_comment)
        await db.flush()

        comment_id = new_comment.id
        logging.info(f"Comment created successfully for refer_id: {refer_id}.")

        return {
            "status": "SUCCESS",
            "message": f"Comment created successfully.",
            "comment_id": comment_id,
            "refer_id": refer_id,
        }
    except SQLAlchemyError as e:
        logging.error(
            f"Database error in add_referral_comment_in_db for refer_id {refer_id}: {e}"
        )
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(
            f"Unexpected error in add_referral_comment_in_db for refer_id {refer_id}: {e}"
        )
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def create_task_for_missing_info(
    db: AsyncSession,
    cmt_id: int,
    patient_id: str,
    refer_id: str,
    priority: str,
    schema_name: str,
    created_task_message: str,
    modified_task_message: str,
    task_status: str = NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS,
    cmt_parent_id: int = None,
) -> Dict[str, Any]:
    """
    Create a task for missing information using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        cmt_id: Comment ID.
        patient_id: Patient ID.
        refer_id: Referral ID.
        priority: Priority level.
        schema_name: Schema name.
        created_task_message: Message for the created task.
        modified_task_message: Message for the modified task.
        task_status: Status of the task.
        cmt_parent_id: Optional parent comment ID for task completion tracking.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        logging.info(f"Creating task for refer_id: {refer_id}.")
        ReferralTaskModel = await get_model(ReferralTask, schema_name)
        current_time = datetime.now(timezone.utc)
        new_task = ReferralTaskModel(
            cmt_id=cmt_id,
            cmt_parent_id=cmt_parent_id,
            patient_id=patient_id,
            refer_id=refer_id,
            task_type=COMMENT_TYPE,
            subject=TASK_SUBJECT,
            status=task_status,
            priority=priority,
            created_date=current_time,
            created_by=CREATED_BY_USER_ID,
            created=CREATED_BY_USERNAME,
            created_task=created_task_message,
            modified_date=current_time,
            modified_by=CREATED_BY_USER_ID,
            modified=CREATED_BY_USERNAME,
            modified_task=modified_task_message,
        )

        db.add(new_task)
        await db.flush()

        task_id = new_task.id
        logging.info(f"Task created successfully for refer_id: {refer_id}.")

        return {
            "status": "SUCCESS",
            "message": f"Task created successfully.",
            "task_id": task_id,
            "refer_id": refer_id,
        }
    except SQLAlchemyError as e:
        logging.error(
            f"Database error in create_task_for_missing_info for refer_id {refer_id}: {e}"
        )
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(
            f"Unexpected error in create_task_for_missing_info for refer_id {refer_id}: {e}"
        )
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def update_created_task_status(
    db: AsyncSession, refer_id: int, schema_name: str
) -> Dict[str, Any]:
    """
    Update the task created status for a referral using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        refer_id: Referral ID.
        schema_name: Schema name.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        logging.info(f"Updating task created status for refer_id: {refer_id}.")
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)
        stmt = select(PatientReferralAIModel).where(
            PatientReferralAIModel.refer_id == refer_id
        )
        patient_referral_ai_record = await db.execute(stmt)
        patient_referral_ai_record = patient_referral_ai_record.scalar_one_or_none()

        if not patient_referral_ai_record:
            return {
                "status": "ERROR",
                "message": f"Referral AI record not found for refer_id: {refer_id}",
                "refer_id": refer_id,
            }

        patient_referral_ai_record.task_created = True
        logging.info(f"Task status updated successfully for refer_id: {refer_id}.")

        return {
            "status": "SUCCESS",
            "message": f"Task created status updated for refer_id: {refer_id}",
            "refer_id": refer_id,
        }
    except SQLAlchemyError as e:
        logging.error(
            f"Database error in update_created_task_status for refer_id {refer_id}: {e}"
        )
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(
            f"Unexpected error in update_created_task_status for refer_id {refer_id}: {e}"
        )
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def get_referrals_with_auto_complete_tasks(
    schema_name: str, db: AsyncSession
) -> List[Dict[str, Any]]:
    """
    Get all the tasks that need to be auto-completed.

    Args:
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: List of rows that meet the criteria.
    """
    try:
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)
        ReferralTaskModel = await get_model(ReferralTask, schema_name)

        query = (
            select(
                PatientReferralAIModel.refer_id,
                PatientReferralAIModel.patient_id,
            )
            .join(
                ReferralTaskModel,
                PatientReferralAIModel.refer_id == ReferralTaskModel.refer_id,
            )
            .where(
                and_(
                    PatientReferralAIModel.require_task == False,
                    PatientReferralAIModel.task_created == True,
                    ReferralTaskModel.status != "Complete",
                    ReferralTaskModel.subject == TASK_SUBJECT,
                )
            )
        )

        patient_referral_ai_record = await db.execute(query)
        rows = patient_referral_ai_record.fetchall()
        columns = patient_referral_ai_record.keys()

        referrals_with_tasks_to_auto_complete = [
            dict(zip(columns, row)) for row in rows
        ]
        logging.info(
            f"Retrieved {len(referrals_with_tasks_to_auto_complete)} referrals with tasks to auto-complete."
        )
        return referrals_with_tasks_to_auto_complete

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def mark_tasks_as_completed(
    refer_id: int, schema_name: str, db: AsyncSession
) -> Dict[str, Any]:
    """
    Mark all tasks for a referral as complete by creating a new comment with parent reference
    and updating the task with proper comment relationships.

    Args:
        refer_id: Referral ID.
        schema_name: Schema name.
        db: SQLAlchemy async database session.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        ReferralCommentModel = await get_model(ReferralComment, schema_name)
        ReferralTaskModel = await get_model(ReferralTask, schema_name)

        current_time = datetime.now(timezone.utc)

        parent_comment_query = (
            select(ReferralCommentModel)
            .where(
                and_(
                    ReferralCommentModel.refer_id == refer_id,
                    ReferralCommentModel.created_by == CREATED_BY_USER_ID,
                    ReferralCommentModel.subject == TASK_SUBJECT,
                    ReferralCommentModel.status
                    == NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS,
                )
            )
            .order_by(ReferralCommentModel.created_date.desc())
        )

        parent_comment_result = await db.execute(parent_comment_query)
        parent_comment = parent_comment_result.scalar_one_or_none()

        if not parent_comment:
            return {
                "status": "ERROR",
                "message": f"No parent comment found for refer_id: {refer_id}",
                "refer_id": refer_id,
            }

        updated_comment_text = "AI Agent found and filled all the missing data points."

        referral_comment = await add_referral_comment_in_db(
            db=db,
            patient_id=parent_comment.patient_id,
            refer_id=refer_id,
            priority=parent_comment.priority,
            schema_name=schema_name,
            comment=updated_comment_text,
            comment_status="Complete",
            parent_id=parent_comment.id,
        )
        referral_comment_id = referral_comment["comment_id"]

        stmt = (
            update(ReferralTaskModel)
            .where(
                and_(
                    ReferralTaskModel.refer_id == refer_id,
                    ReferralTaskModel.status != "Complete",
                    ReferralTaskModel.subject == TASK_SUBJECT,
                )
            )
            .values(
                cmt_parent_id=parent_comment.id,
                cmt_id=referral_comment_id,
                status="Complete",
                modified_date=current_time,
                modified_task=updated_comment_text,
            )
            .execution_options(synchronize_session=False)
        )

        await db.execute(stmt)
        await db.commit()

        return {
            "status": "SUCCESS",
            "message": f"Tasks marked as complete for refer_id: {refer_id}",
            "refer_id": refer_id,
            "parent_comment_id": parent_comment.id,
            "new_comment_id": referral_comment_id,
        }

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def create_completed_task_for_visibility(
    db: AsyncSession,
    refer_id: int,
    patient_id: int,
    filled_fields: List[str],
    schema_name: str,
) -> Dict[str, Any]:
    """
    Create a completed task for visibility when AI finds and fills missing information
    but no manual task is required (require_task = False).

    Args:
        db: SQLAlchemy async database session.
        refer_id: Referral ID.
        patient_id: Patient ID.
        filled_fields: List of fields that were filled by AI.
        schema_name: Schema name.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        priority = (
            "PD"
            if not set(filled_fields).isdisjoint(set(HIGH_PRIORITY_FIELDS))
            else "N"
        )
        filled_fields = map_missing_field_to_labels(filled_fields)
        data_point_description = ", ".join(filled_fields)
        completed_task_message = f"AI Agent found and filled the missing data points: {data_point_description}"
        task_response = await create_task_for_single_referral(
            db=db,
            refer_id=refer_id,
            patient_id=patient_id,
            client_schema=schema_name,
            priority=priority,
            created_task_message=completed_task_message,
            modified_task_message=completed_task_message,
            task_status="Complete",
            comment_status="Complete",
        )
        logging.info(f"Completed task created for visibility for refer_id: {refer_id}.")
        return task_response

    except SQLAlchemyError as e:
        logging.error(
            f"Database error in create_completed_task_for_visibility for refer_id {refer_id}: {e}"
        )
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(
            f"Unexpected error in create_completed_task_for_visibility for refer_id {refer_id}: {e}"
        )
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )
