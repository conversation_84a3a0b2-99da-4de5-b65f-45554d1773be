from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
import xml.etree.ElementTree as ET
from constants import *


@dataclass
class ClientConfiguration:
    """Data class to hold client-specific configuration values."""
    
    # Basic settings
    root_path: str = ROOT_PATH
    server_name: str = SERVER_NAME
    
    # Authentication
    jwt_secret_key: str = JWT_SECRET_KEY
    encryption_algorithm: str = ENCRYPTION_ALGORITHM
    access_token_expire_minutes: int = ACCESS_TOKEN_EXPIRE_MINUTES
    refresh_token_expire_minutes: int = REFRESH_TOKEN_EXPIRE_MINUTES
    auth_credentials: str = AUTH_CREDENTIALS
    auth_keys_delimiter: str = AUTH_KEYS_DELIMITER
    swagger_basic_auth_key: str = SWAGGER_BASIC_AUTH_KEY
    
    # Database
    db_host: str = DB_HOST
    db_port: str = DB_PORT
    db_name: str = DB_NAME
    db_user: str = DB_USER
    db_pass: str = DB_PASS
    db_pool_size: int = DB_POOL_SIZE
    max_overflow_connections: int = MAX_OVERFLOW_CONNECTIONS
    pool_timeout: int = POOL_TIMEOUT
    
    # Workers
    client_workers: int = CLIENT_WORKERS
    referral_processing_workers: int = REFERRAL_PROCESSING_WORKERS
    
    # PDFPlus
    pdfplus_url: str = PDFPLUS_URL
    pdfplus_extract_fields_endpoint: str = PDFPLUS_EXTRACT_FIELDS_ENDPOINT
    pdfplus_auth_key: str = PDFPLUS_AUTH_KEY
    
    # Missing Info Agent
    disable_missing_info_agent: str = DISABLE_MISSING_INFO_AGENT
    disable_missing_info_new_referral_workflow: str = DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW
    disable_missing_info_retrigger_workflow: str = DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW
    missing_fields_to_look: List[str] = field(default_factory=lambda: MISSING_FIELDS_TO_LOOK)
    fields_mapping_prompt: List[str] = field(default_factory=lambda: FIELDS_MAPPING_PROMPT)
    missing_fields_labels: List[str] = field(default_factory=lambda: MISSING_FIELDS_LABELS)
    high_priority_fields: List[str] = field(default_factory=lambda: HIGH_PRIORITY_FIELDS)
    
    # Task Workflow Agent
    task_subject: str = TASK_SUBJECT
    new_comment_task_status_for_missing_fields: str = NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS
    comment: str = COMMENT
    comment_type: str = COMMENT_TYPE
    created_by_username: str = CREATED_BY_USERNAME
    created_by_user_id: int = CREATED_BY_USER_ID
    task_creation_waiting_minutes: int = TASK_CREATION_WAITING_MINUTES
    disable_task_creation_workflow: str = DISABLE_TASK_CREATION_WORKFLOW
    disable_task_and_workflow_agent: str = DISABLE_TASK_AND_WORKFLOW_AGENT
    
    # Prioritization Agent
    disable_prioritization_agent: str = DISABLE_PRIORITIZATION_AGENT
    disable_auto_decline_workflow: str = DISABLE_AUTO_DECLINE_WORKFLOW
    disable_patient_to_facility_distance_decline: str = DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE
    disable_source_to_facility_distance_decline: str = DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE
    disable_shotgun_score_decline: str = DISABLE_SHOTGUN_SCORE_DECLINE
    disable_referral_payer_decline: str = DISABLE_REFERRAL_PAYER_DECLINE
    disable_referral_source_decline: str = DISABLE_REFERRAL_SOURCE_DECLINE
    
    # Client identification
    client_id: Optional[str] = None
    client_schema: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClientConfiguration':
        """Create configuration from dictionary."""
        # Filter out keys that don't exist in the dataclass
        valid_keys = {field.name for field in cls.__dataclass_fields__.values()}
        filtered_data = {k: v for k, v in data.items() if k in valid_keys}
        return cls(**filtered_data)


class ClientConfigurationParser:
    """Utility class to parse client configuration from XML."""
    
    @staticmethod
    def parse_xml_config(xml_content: str) -> Dict[str, Any]:
        """
        Parse XML configuration content and return a dictionary.
        
        Args:
            xml_content: XML string containing client configuration
            
        Returns:
            Dictionary with parsed configuration values
        """
        try:
            root = ET.fromstring(xml_content)
            config = {}
            
            # Parse basic settings
            if root.find('.//RootPath') is not None:
                config['root_path'] = root.find('.//RootPath').text
            if root.find('.//ServerName') is not None:
                config['server_name'] = root.find('.//ServerName').text
            
            # Parse authentication settings
            auth_section = root.find('.//Authentication')
            if auth_section is not None:
                if auth_section.find('JwtSecretKey') is not None and auth_section.find('JwtSecretKey').text:
                    config['jwt_secret_key'] = auth_section.find('JwtSecretKey').text
                if auth_section.find('EncryptionAlgorithm') is not None:
                    config['encryption_algorithm'] = auth_section.find('EncryptionAlgorithm').text
                if auth_section.find('AccessTokenExpireMinutes') is not None:
                    config['access_token_expire_minutes'] = int(auth_section.find('AccessTokenExpireMinutes').text)
                if auth_section.find('RefreshTokenExpireMinutes') is not None:
                    config['refresh_token_expire_minutes'] = int(auth_section.find('RefreshTokenExpireMinutes').text)
                if auth_section.find('AuthCredentials') is not None and auth_section.find('AuthCredentials').text:
                    config['auth_credentials'] = auth_section.find('AuthCredentials').text
                if auth_section.find('AuthKeysDelimiter') is not None and auth_section.find('AuthKeysDelimiter').text:
                    config['auth_keys_delimiter'] = auth_section.find('AuthKeysDelimiter').text
                if auth_section.find('SwaggerBasicAuthKey') is not None and auth_section.find('SwaggerBasicAuthKey').text:
                    config['swagger_basic_auth_key'] = auth_section.find('SwaggerBasicAuthKey').text
            
            # Parse database settings
            db_section = root.find('.//Database')
            if db_section is not None:
                if db_section.find('Host') is not None and db_section.find('Host').text:
                    config['db_host'] = db_section.find('Host').text
                if db_section.find('Port') is not None and db_section.find('Port').text:
                    config['db_port'] = db_section.find('Port').text
                if db_section.find('Name') is not None and db_section.find('Name').text:
                    config['db_name'] = db_section.find('Name').text
                if db_section.find('User') is not None and db_section.find('User').text:
                    config['db_user'] = db_section.find('User').text
                if db_section.find('Password') is not None and db_section.find('Password').text:
                    config['db_pass'] = db_section.find('Password').text
                if db_section.find('PoolSize') is not None:
                    config['db_pool_size'] = int(db_section.find('PoolSize').text)
                if db_section.find('MaxOverflowConnections') is not None:
                    config['max_overflow_connections'] = int(db_section.find('MaxOverflowConnections').text)
                if db_section.find('PoolTimeout') is not None:
                    config['pool_timeout'] = int(db_section.find('PoolTimeout').text)
            
            # Parse workers settings
            workers_section = root.find('.//Workers')
            if workers_section is not None:
                if workers_section.find('ClientWorkers') is not None:
                    config['client_workers'] = int(workers_section.find('ClientWorkers').text)
                if workers_section.find('ReferralProcessingWorkers') is not None:
                    config['referral_processing_workers'] = int(workers_section.find('ReferralProcessingWorkers').text)
            
            # Parse PDFPlus settings
            pdfplus_section = root.find('.//PDFPlus')
            if pdfplus_section is not None:
                if pdfplus_section.find('BaseUrl') is not None:
                    config['pdfplus_url'] = pdfplus_section.find('BaseUrl').text
                if pdfplus_section.find('ExtractFieldsEndpoint') is not None:
                    config['pdfplus_extract_fields_endpoint'] = pdfplus_section.find('ExtractFieldsEndpoint').text
                if pdfplus_section.find('AuthKey') is not None and pdfplus_section.find('AuthKey').text:
                    config['pdfplus_auth_key'] = pdfplus_section.find('AuthKey').text
            
            # Parse Missing Info Agent settings
            missing_info_section = root.find('.//MissingInfoAgent')
            if missing_info_section is not None:
                if missing_info_section.find('DisableAgent') is not None:
                    config['disable_missing_info_agent'] = missing_info_section.find('DisableAgent').text.lower()
                if missing_info_section.find('DisableNewReferralWorkflow') is not None:
                    config['disable_missing_info_new_referral_workflow'] = missing_info_section.find('DisableNewReferralWorkflow').text.lower()
                if missing_info_section.find('DisableRetriggerWorkflow') is not None:
                    config['disable_missing_info_retrigger_workflow'] = missing_info_section.find('DisableRetriggerWorkflow').text.lower()
                
                # Parse field mappings
                field_mappings = missing_info_section.find('.//FieldMappings')
                if field_mappings is not None:
                    columns = []
                    prompts = []
                    labels = []
                    high_priority = []
                    
                    for field in field_mappings.findall('Field'):
                        column = field.find('Column')
                        prompt = field.find('Prompt')
                        label = field.find('Label')
                        is_high_priority = field.find('HighPriority')
                        
                        if column is not None and column.text:
                            columns.append(column.text)
                        if prompt is not None and prompt.text:
                            prompts.append(prompt.text)
                        if label is not None and label.text:
                            labels.append(label.text)
                        if is_high_priority is not None and is_high_priority.text == 'true' and column is not None:
                            high_priority.append(column.text)
                    
                    if columns:
                        config['missing_fields_to_look'] = columns
                    if prompts:
                        config['fields_mapping_prompt'] = prompts
                    if labels:
                        config['missing_fields_labels'] = labels
                    if high_priority:
                        config['high_priority_fields'] = high_priority
            
            return config
            
        except ET.ParseError as e:
            raise ValueError(f"Invalid XML configuration: {e}")
        except Exception as e:
            raise ValueError(f"Error parsing configuration: {e}")
    
    @staticmethod
    def merge_with_defaults(client_config: Dict[str, Any], default_config: Optional[ClientConfiguration] = None) -> ClientConfiguration:
        """
        Merge client configuration with default values.
        
        Args:
            client_config: Dictionary with client-specific configuration
            default_config: Default configuration object (if None, uses default ClientConfiguration)
            
        Returns:
            Merged ClientConfiguration object
        """
        if default_config is None:
            default_config = ClientConfiguration()
        
        # Convert default config to dict and update with client config
        merged_dict = default_config.to_dict()
        merged_dict.update(client_config)
        
        return ClientConfiguration.from_dict(merged_dict)
