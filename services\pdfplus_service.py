from typing import Any, Dict, List
import json
import logging
import urllib.parse
import httpx
from fastapi import Request
from utils.config_utils import get_config_from_request
from services.client_config_service import get_client_config_service
from utils.fields_mapping import (
    get_missing_fields_mapping,
    map_missing_fields_to_prompts,
    map_api_response_to_columns,
)
import re


async def call_pdfplus_extract_fields_api(
    refer_id: str, ocr_data: str, missing_fields: List[str], request: Request = None
) -> Dict[str, Any]:
    """
    Extract missing fields from OCR data using the PDFPlus API.
    Args:
        refer_id: The refer_id of the referral.
        ocr_data: The OCR data of the referral.
        missing_fields: The list of missing fields to extract.
        request: FastAPI request object for client configuration.
    Returns:
        Dict[str, Any]: The extracted missing fields.
    """
    logging.info(
        f"Calling PDFPlus API for refer_id={refer_id}, missing_fields={missing_fields}"
    )

    prompts_mapping = get_missing_fields_mapping(request)
    prompts = map_missing_fields_to_prompts(missing_fields, prompts_mapping)
    response = await send_pdfplus_request(refer_id, ocr_data, prompts, request)

    if not response:
        return {}

    mapped_response = map_api_response_to_columns(response, prompts, prompts_mapping)

    if "resmce_id" in mapped_response:
        if (
            not is_valid_medicare_id(mapped_response["resmce_id"])
            or not mapped_response["resmce_id"]
        ):
            logging.info(
                f"Invalid or no medicare_id found for refer_id={refer_id}. Parsing OCR data to find MBI."
            )
            mapped_response["resmce_id"] = parse_mbi_from_ocr_data(ocr_data) or None

    return mapped_response


async def send_pdfplus_request(
    refer_id: str, ocr_data: str, prompts: List[str], request: Request = None
) -> Dict[str, Any]:
    """
    Send a request to the PDFPlus API to extract missing fields from OCR data.
    Args:
        refer_id: The refer_id of the referral.
        ocr_data: The OCR data of the referral.
        prompts: The list of prompts to extract.
        request: FastAPI request object for client configuration.
    Returns:
        Dict[str, Any]: The response from the API.
    """
    # Get configuration (client-specific or default)
    if request:
        config = get_config_from_request(request)
        pdfplus_auth_key = config.pdfplus_auth_key
        pdfplus_url = config.pdfplus_url
        pdfplus_extract_fields_endpoint = config.pdfplus_extract_fields_endpoint
        pdfplus_connection_timeout = config.pdfplus_connection_timeout
        pdfplus_read_timeout = config.pdfplus_read_timeout
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        pdfplus_auth_key = default_config.pdfplus_auth_key
        pdfplus_url = default_config.pdfplus_url
        pdfplus_extract_fields_endpoint = default_config.pdfplus_extract_fields_endpoint
        pdfplus_connection_timeout = default_config.pdfplus_connection_timeout
        pdfplus_read_timeout = default_config.pdfplus_read_timeout

    files = {"ocr_data": ("ocr_data.json", json.dumps(ocr_data), "application/json")}
    form_data = {"data_points": json.dumps({"questions": prompts})}

    async with httpx.AsyncClient(
        timeout=httpx.Timeout(
            connect=pdfplus_connection_timeout,
            read=pdfplus_read_timeout,
            pool=None,
            write=None,
        )
    ) as client:
        try:
            response = await client.post(
                url=urllib.parse.urljoin(
                    pdfplus_url.rstrip("/"), pdfplus_extract_fields_endpoint.lstrip("/")
                ),
                data=form_data,
                files=files,
                headers={"Authorization": f"Basic {pdfplus_auth_key}"},
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logging.error(f"API call failed for refer_id={refer_id}: {e.response.text}")
            return {}
        except httpx.ReadTimeout as e:
            logging.error(
                f"API call failed for refer_id={refer_id} due to timeout: {e}"
            )
            return {}
        except Exception as e:
            logging.error(f"API call failed for refer_id={refer_id}: {e}")
            return {}


def is_valid_medicare_id(medicare_id: str) -> bool:
    """
    Return True if medicare_id matches the CMS 11-char Medicare ID format, else False.
    Args:
        medicare_id: The medicare_id to validate.
    Returns:
        bool: True if medicare_id is valid, else False.
    """
    pattern = re.compile(
        r"^[1-9]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9]-?"
        r"[0-9]-?$"
    )
    return bool(pattern.match(medicare_id.strip()))


def parse_mbi_from_ocr_data(ocr_data: Dict[str, Any]) -> str:
    """
    Parse the OCR data to find the MBI. If no MBI is found, return an empty string.
    Args:
        ocr_data: The OCR data to parse.
    Returns:
        str: The MBI found in the OCR data. If no MBI is found, return an empty string.
    """
    MBI_PATTERN = re.compile(
        r"\b"
        r"[1-9]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[AC-FG-HJKMNP-RT-Y]-?"
        r"[0-9]-?"
        r"[0-9]"
        r"\b"
    )
    for page_number, page_content in ocr_data.items():
        matches = MBI_PATTERN.findall(page_content["content"])
        if matches:
            logging.info(f"Found MBI in OCR data on page {page_number}.")
            return matches[0].replace("-", "")
    return ""
