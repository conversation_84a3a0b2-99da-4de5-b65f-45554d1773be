from fastapi import Request
import time
import logging
from typing import Callable, List, Dict, Any
from fastapi import Response
import uuid
from utils.logging_config import REQUEST_ID_VAR
from constants import *


async def http_middleware(request: Request, call_next: Callable) -> Response:
    """
    The HTTP middleware function.
    Args:
        request (Request): The request object.
        call_next (Callable): The next middleware function.
    Returns:
        Response: The response object.
    """
    REQUEST_ID_VAR.set(str(uuid.uuid4()))

    start_time = time.time()

    request.client_configurations = {
        "client1_schema": {
            # Default configuration (from constants.py)
            "ROOT_PATH": ROOT_PATH,
            "SERVER_NAME": SERVER_NAME,
            # Authentication
            "JWT_SECRET_KEY": JWT_SECRET_KEY,
            "ENCRYPTION_ALGORITHM": ENCRYPTION_ALGORITHM,
            "ACCESS_TOKEN_EXPIRE_MINUTES": ACCESS_TOKEN_EXPIRE_MINUTES,
            "REFRESH_TOKEN_EXPIRE_MINUTES": REFRESH_TOKEN_EXPIRE_MINUTES,
            "AUTH_CREDENTIALS": AUTH_CREDENTIALS,
            "AUTH_KEYS_DELIMITER": AUTH_KEYS_DELIMITER,
            "SWAGGER_BASIC_AUTH_KEY": SWAGGER_BASIC_AUTH_KEY,
            # Database Connection
            "DB_HOST": DB_HOST,
            "DB_PORT": DB_PORT,
            "DB_NAME": DB_NAME,
            "DB_USER": DB_USER,
            "DB_PASS": DB_PASS,
            "DB_POOL_SIZE": DB_POOL_SIZE,
            "MAX_OVERFLOW_CONNECTIONS": MAX_OVERFLOW_CONNECTIONS,
            "POOL_TIMEOUT": POOL_TIMEOUT,
            # Workers
            "CLIENT_WORKERS": CLIENT_WORKERS,
            "REFERRAL_PROCESSING_WORKERS": REFERRAL_PROCESSING_WORKERS,
            # PDFPlus
            "PDFPLUS_URL": PDFPLUS_URL,
            "PDFPLUS_EXTRACT_FIELDS_ENDPOINT": PDFPLUS_EXTRACT_FIELDS_ENDPOINT,
            "PDFPLUS_AUTH_KEY": PDFPLUS_AUTH_KEY,
            "PDFPLUS_CONNECTION_TIMEOUT": PDFPLUS_CONNECTION_TIMEOUT,
            "PDFPLUS_READ_TIMEOUT": PDFPLUS_READ_TIMEOUT,
            # Missing Info Agent
            "DISABLE_MISSING_INFO_AGENT": DISABLE_MISSING_INFO_AGENT,
            "DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW": DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW,
            "DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW": DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW,
            "MISSING_FIELDS_TO_LOOK": MISSING_FIELDS_TO_LOOK,
            "FIELDS_MAPPING_PROMPT": FIELDS_MAPPING_PROMPT,
            "MISSING_FIELDS_LABELS": MISSING_FIELDS_LABELS,
            "HIGH_PRIORITY_FIELDS": HIGH_PRIORITY_FIELDS,
            # Task Workflow Agent
            "TASK_SUBJECT": TASK_SUBJECT,
            "NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS": NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS,
            "COMMENT": COMMENT,
            "COMMENT_TYPE": COMMENT_TYPE,
            "CREATED_BY_USERNAME": CREATED_BY_USERNAME,
            "CREATED_BY_USER_ID": CREATED_BY_USER_ID,
            "TASK_CREATION_WAITING_MINUTES": TASK_CREATION_WAITING_MINUTES,
            "DISABLE_TASK_CREATION_WORKFLOW": DISABLE_TASK_CREATION_WORKFLOW,
            "DISABLE_TASK_AND_WORKFLOW_AGENT": DISABLE_TASK_AND_WORKFLOW_AGENT,
            # Prioritization Agent
            "DISABLE_PRIORITIZATION_AGENT": DISABLE_PRIORITIZATION_AGENT,
            "DISABLE_AUTO_DECLINE_WORKFLOW": DISABLE_AUTO_DECLINE_WORKFLOW,
            "DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE": DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE,
            "DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE": DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE,
            "DISABLE_SHOTGUN_SCORE_DECLINE": DISABLE_SHOTGUN_SCORE_DECLINE,
            "DISABLE_REFERRAL_PAYER_DECLINE": DISABLE_REFERRAL_PAYER_DECLINE,
            "DISABLE_REFERRAL_SOURCE_DECLINE": DISABLE_REFERRAL_SOURCE_DECLINE,
            "DISABLE_REFERRAL_TOTAL_SCORE_DECLINE": DISABLE_REFERRAL_TOTAL_SCORE_DECLINE,
            # Thresholds
            "SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES": SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES,
            "PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES": PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES,
            "SHOTGUN_SCORE_THRESHOLD": SHOTGUN_SCORE_THRESHOLD,
            "REFERRAL_PAYER_SCORE_THRESHOLD": REFERRAL_PAYER_SCORE_THRESHOLD,
            "REFERRAL_SOURCE_SCORE_THRESHOLD": REFERRAL_SOURCE_SCORE_THRESHOLD,
            "REFERRAL_TOTAL_SCORE_THRESHOLD": REFERRAL_TOTAL_SCORE_THRESHOLD,
            # Error Monitoring
            "DISABLE_ERROR_MONITORING": DISABLE_ERROR_MONITORING,
            "ERROR_ALERT_INTERVAL_MINUTES": ERROR_ALERT_INTERVAL_MINUTES,
            "ERROR_ALERT_SUBJECT": ERROR_ALERT_SUBJECT,
            "EMAIL_TO_USER": EMAIL_TO_USER,
            "EMAIL_FROM_USER": EMAIL_FROM_USER,
            "ENABLE_POSTFIX_EMAIL": ENABLE_POSTFIX_EMAIL,
            "EMAIL_AUTH_USER": EMAIL_AUTH_USER,
            "EMAIL_APP_PASSWORD": EMAIL_APP_PASSWORD,
            # Scheduler
            "DISABLE_INTERNAL_SCHEDULER": DISABLE_INTERNAL_SCHEDULER,
            "ERROR_MONITORING_INTERVAL_SECONDS": ERROR_MONITORING_INTERVAL_SECONDS,
            "MISSING_INFO_AGENT_INTERVAL_SECONDS": MISSING_INFO_AGENT_INTERVAL_SECONDS,
            "TASK_WORKFLOW_AGENT_INTERVAL_SECONDS": TASK_WORKFLOW_AGENT_INTERVAL_SECONDS,
            "PRIORITIZATION_AGENT_INTERVAL_SECONDS": PRIORITIZATION_AGENT_INTERVAL_SECONDS,
        },
        "client2_schema": {
            # Alternative configuration with different values
            "ROOT_PATH": "/referral-ai-alt",
            "SERVER_NAME": "production-server",
            # Authentication - different values
            "JWT_SECRET_KEY": "ProductionSecretKey",
            "ENCRYPTION_ALGORITHM": "HS256",
            "ACCESS_TOKEN_EXPIRE_MINUTES": 60,  # 1 hour instead of 3
            "REFRESH_TOKEN_EXPIRE_MINUTES": 1440,  # 1 day instead of 7
            "AUTH_CREDENTIALS": "prod:$2b$12$differentHashedPassword",
            "AUTH_KEYS_DELIMITER": "##",
            "SWAGGER_BASIC_AUTH_KEY": "cHJvZDpwYXNzd29yZA==",
            # Database Connection - different values
            "DB_HOST": "prod-db.example.com",
            "DB_PORT": "5433",
            "DB_NAME": "production_lincdoc",
            "DB_USER": "prod_user",
            "DB_PASS": "prod_password",
            "DB_POOL_SIZE": 10,
            "MAX_OVERFLOW_CONNECTIONS": 10,
            "POOL_TIMEOUT": 30,
            # Workers - different values
            "CLIENT_WORKERS": 4,
            "REFERRAL_PROCESSING_WORKERS": 20,
            # PDFPlus - different values
            "PDFPLUS_URL": "https://prod-pdfplus.example.com",
            "PDFPLUS_EXTRACT_FIELDS_ENDPOINT": "api/extract-fields",
            "PDFPLUS_AUTH_KEY": "prod-auth-key",
            "PDFPLUS_CONNECTION_TIMEOUT": 60,
            "PDFPLUS_READ_TIMEOUT": 300,
            # Missing Info Agent - different values
            "DISABLE_MISSING_INFO_AGENT": "false",
            "DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW": "false",
            "DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW": "false",
            "MISSING_FIELDS_TO_LOOK": ["patient_ssn", "medicare_id", "patient_address"],
            "FIELDS_MAPPING_PROMPT": [
                "Social Security Number",
                "Medicare ID",
                "Full Address",
            ],
            "MISSING_FIELDS_LABELS": ["SSN", "Medicare", "Address"],
            "HIGH_PRIORITY_FIELDS": ["patient_ssn", "medicare_id"],
            # Task Workflow Agent - different values
            "TASK_SUBJECT": "Critical Missing Information",
            "NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS": "High Priority",
            "COMMENT": "Urgent: Please provide {data_point} for patient referral.",
            "COMMENT_TYPE": "Critical",
            "CREATED_BY_USERNAME": "ProductionAI",
            "CREATED_BY_USER_ID": -1,
            "TASK_CREATION_WAITING_MINUTES": 60,
            "DISABLE_TASK_CREATION_WORKFLOW": "false",
            "DISABLE_TASK_AND_WORKFLOW_AGENT": "false",
            # Prioritization Agent - different values
            "DISABLE_PRIORITIZATION_AGENT": "false",
            "DISABLE_AUTO_DECLINE_WORKFLOW": "false",
            "DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE": "false",
            "DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE": "false",
            "DISABLE_SHOTGUN_SCORE_DECLINE": "false",
            "DISABLE_REFERRAL_PAYER_DECLINE": "false",
            "DISABLE_REFERRAL_SOURCE_DECLINE": "false",
            "DISABLE_REFERRAL_TOTAL_SCORE_DECLINE": "false",
            # Thresholds - different values
            "SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES": 50.0,
            "PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES": 75.0,
            "SHOTGUN_SCORE_THRESHOLD": 15.0,
            "REFERRAL_PAYER_SCORE_THRESHOLD": 25.0,
            "REFERRAL_SOURCE_SCORE_THRESHOLD": 30.0,
            "REFERRAL_TOTAL_SCORE_THRESHOLD": 35.0,
            # Error Monitoring - different values
            "DISABLE_ERROR_MONITORING": "false",
            "ERROR_ALERT_INTERVAL_MINUTES": 60,
            "ERROR_ALERT_SUBJECT": "🔥 PRODUCTION ERROR: Immediate attention required!",
            "EMAIL_TO_USER": "<EMAIL>",
            "EMAIL_FROM_USER": "<EMAIL>",
            "ENABLE_POSTFIX_EMAIL": "true",
            "EMAIL_AUTH_USER": "prod_email",
            "EMAIL_APP_PASSWORD": "prod_email_pass",
            # Scheduler - different values
            "DISABLE_INTERNAL_SCHEDULER": "false",
            "ERROR_MONITORING_INTERVAL_SECONDS": 60,
            "MISSING_INFO_AGENT_INTERVAL_SECONDS": 300,
            "TASK_WORKFLOW_AGENT_INTERVAL_SECONDS": 300,
            "PRIORITIZATION_AGENT_INTERVAL_SECONDS": 300,
        },
    }

    client_host = request.client.host
    url_path = request.url.path
    logging.info(f"started: {client_host} | {request.method} | {url_path}")

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
