from fastapi import APIRouter, Request
from fastapi import Depends
from fastapi.security import HTTPBasicCredentials
from auth import authenticate_swagger_calls
from ratelimit import limiter
from fastapi.openapi.docs import get_swagger_ui_html
from helpers.version_helper import get_version
from utils.config_utils import get_config_from_request


swagger_router = APIRouter()


@swagger_router.get("/docs", include_in_schema=False)
@limiter.limit("5/minute")
async def custom_swagger_ui_html(
    request: Request,
    credentials: HTTPBasicCredentials = Depends(authenticate_swagger_calls),
):
    config = get_config_from_request(request)
    return get_swagger_ui_html(
        openapi_url=f"{config.root_path}/openapi.json",
        title=f"Referral AI {get_version()}",
    )


@swagger_router.get("/openapi.json", include_in_schema=False)
@limiter.limit("5/minute")
async def custom_openapi(
    request: Request,
    credentials: HTTPBasicCredentials = Depends(authenticate_swagger_calls),
):
    config = get_config_from_request(request)
    openapi_schema = request.app.openapi()
    openapi_schema["servers"] = [
        {"url": config.root_path},
    ]
    return openapi_schema
