from constants import (
    MISSING_FIELDS_TO_LOOK,
    FIELDS_MAPPING_PROMPT,
    MISSING_FIELDS_LABELS,
)
from typing import Dict, <PERSON><PERSON>, Any, List


def get_missing_fields_mapping() -> <PERSON>ple[Dict[str, str], Dict[str, str]]:
    """
    Get the mapping of missing fields to their prompts.
    Args:
        None
    Returns:
        Tuple[Dict[str, str], Dict[str, str]]: The mapping of missing fields to their prompts and reverse mapping.
    """
    if len(MISSING_FIELDS_TO_LOOK) != len(FIELDS_MAPPING_PROMPT):
        raise ValueError(
            "Each MISSING_FIELDS_TO_LOOK should have a mapping prompt in FIELDS_MAPPING_PROMPT."
        )
    columns_to_prompts_mappings = dict(
        zip(MISSING_FIELDS_TO_LOOK, FIELDS_MAPPING_PROMPT)
    )
    prompts_to_columns_mappings = dict(
        zip(FIELDS_MAPPING_PROMPT, MISSING_FIELDS_TO_LOOK)
    )
    return columns_to_prompts_mappings, prompts_to_columns_mappings


def map_api_response_to_columns(
    response: Dict[str, Any], prompts: List[str], mappings
) -> Dict[str, Any]:
    """Map API response fields back to original column names."""
    _, prompts_to_columns = mappings
    return {prompts_to_columns[prompt]: response[prompt] for prompt in prompts}


def map_missing_fields_to_prompts(missing_fields: List[str], mappings) -> List[str]:
    """Convert missing field names to API prompts."""
    columns_to_prompts, _ = mappings
    return [columns_to_prompts[field] for field in missing_fields]


def map_missing_field_to_labels(missing_data_points: List[str]) -> List[str]:
    """
    Convert DB field names in `missing_data_points` to human-readable labels
    using MISSING_FIELDS_TO_LOOK and MISSING_FIELDS_LABELS.
    Args:
        missing_data_points: List of field keys (e.g., ['res_ssn', 'res_acsz'])
    Returns:
        List of corresponding labels (e.g., ['SSN', 'Patient Address'])
    """
    field_to_label = dict(zip(MISSING_FIELDS_TO_LOOK, MISSING_FIELDS_LABELS))
    return [
        field_to_label[field]
        for field in missing_data_points
        if field in field_to_label
    ]
