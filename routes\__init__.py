from routes.index import index_router
from routes.health_check import health_check_router
from routes.login import login_router
from routes.missing_info_agent import missing_info_agent_router
from routes.prioritization_agent import prioritization_agent_router
from routes.secure_data import secure_data_router
from routes.monitor import monitor_router
from routes.swagger import swagger_router
from routes.config import config_router
from routes.task_management_and_workflow_agent import (
    task_management_and_workflow_agent_router,
)

all_routers = [
    index_router,
    health_check_router,
    login_router,
    missing_info_agent_router,
    prioritization_agent_router,
    task_management_and_workflow_agent_router,
    secure_data_router,
    monitor_router,
    config_router,
    swagger_router,
]
