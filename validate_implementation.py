#!/usr/bin/env python3
"""
Validation script for the client configuration system.
This script tests the core functionality without requiring a full test framework.
"""

import asyncio
import sys
from unittest.mock import Mock

# Import our modules
from models.client_config import ClientConfiguration, ClientConfigurationParser
from services.client_config_service import (
    ClientConfigurationService,
    get_client_config_service,
)
from middleware.client_identification import ClientIdentificationService
from utils.config_utils import get_config_from_request, get_missing_fields_to_look


def test_client_configuration():
    """Test ClientConfiguration model."""
    print("Testing ClientConfiguration model...")

    # Test default configuration
    config = ClientConfiguration()
    assert config.root_path == "/referral-ai"
    assert config.server_name == "localhost"
    assert config.client_id is None
    print("✓ Default configuration works")

    # Test custom configuration
    config = ClientConfiguration(
        client_id="test_client", client_schema="test_schema", root_path="/custom-path"
    )
    assert config.client_id == "test_client"
    assert config.client_schema == "test_schema"
    assert config.root_path == "/custom-path"
    print("✓ Custom configuration works")

    # Test to_dict and from_dict
    config_dict = config.to_dict()
    assert config_dict["client_id"] == "test_client"

    new_config = ClientConfiguration.from_dict(config_dict)
    assert new_config.client_id == "test_client"
    assert new_config.root_path == "/custom-path"
    print("✓ Dict conversion works")


def test_xml_parser():
    """Test XML configuration parsing."""
    print("\nTesting XML configuration parsing...")

    xml_content = """
    <referralAIClientConfig>
        <AIConfiguration>
            <RootPath>/custom-path</RootPath>
            <ServerName>custom-server</ServerName>
            <Authentication>
                <EncryptionAlgorithm>HS256</EncryptionAlgorithm>
                <AccessTokenExpireMinutes>120</AccessTokenExpireMinutes>
            </Authentication>
            <Database>
                <Host>custom-host</Host>
                <Port>5433</Port>
                <PoolSize>10</PoolSize>
            </Database>
            <WorkflowConfigurations>
                <MissingInfoAgent>
                    <DisableAgent>false</DisableAgent>
                    <FieldMappings>
                        <Field>
                            <Column>ssn</Column>
                            <Prompt>Social Security Number</Prompt>
                            <Label>SSN</Label>
                            <HighPriority>true</HighPriority>
                        </Field>
                        <Field>
                            <Column>address</Column>
                            <Prompt>Patient Address</Prompt>
                            <Label>Address</Label>
                            <HighPriority>false</HighPriority>
                        </Field>
                    </FieldMappings>
                </MissingInfoAgent>
            </WorkflowConfigurations>
        </AIConfiguration>
    </referralAIClientConfig>
    """

    parser = ClientConfigurationParser()
    config_data = parser.parse_xml_config(xml_content)

    assert config_data["root_path"] == "/custom-path"
    assert config_data["server_name"] == "custom-server"
    assert config_data["encryption_algorithm"] == "HS256"
    assert config_data["access_token_expire_minutes"] == 120
    assert config_data["db_host"] == "custom-host"
    assert config_data["db_port"] == "5433"
    assert config_data["db_pool_size"] == 10
    assert config_data["disable_missing_info_agent"] == "false"
    assert config_data["missing_fields_to_look"] == ["ssn", "address"]
    assert config_data["fields_mapping_prompt"] == [
        "Social Security Number",
        "Patient Address",
    ]
    assert config_data["missing_fields_labels"] == ["SSN", "Address"]
    assert config_data["high_priority_fields"] == ["ssn"]
    print("✓ XML parsing works")

    # Test merging with defaults
    merged_config = parser.merge_with_defaults(config_data)
    assert merged_config.root_path == "/custom-path"
    assert merged_config.db_host == "custom-host"
    assert merged_config.missing_fields_to_look == ["ssn", "address"]
    # Should have defaults for non-specified fields
    assert merged_config.refresh_token_expire_minutes == 10080  # default
    print("✓ Merging with defaults works")


async def test_client_identification():
    """Test client identification service."""
    print("\nTesting client identification...")

    # Mock request with header
    mock_request = Mock()
    mock_request.headers = {"X-Client-ID": "test_client"}
    mock_request.url.path = "/api/v1/test"
    mock_request.query_params = {}

    service = ClientIdentificationService()

    # Mock the config service method
    async def mock_get_client_schema(client_id):
        return "test_schema"

    original_method = service.config_service.get_client_schema
    service.config_service.get_client_schema = mock_get_client_schema

    try:
        client_id, client_schema = await service.identify_client_from_request(
            mock_request
        )
        assert client_id == "test_client"
        assert client_schema == "test_schema"
        print("✓ Header-based identification works")
    finally:
        service.config_service.get_client_schema = original_method

    # Test path-based identification
    mock_request.headers = {}
    mock_request.url.path = "/api/v1/client/path_client/data"

    async def mock_get_client_schema_path(client_id):
        return "path_schema"

    service.config_service.get_client_schema = mock_get_client_schema_path

    try:
        client_id, client_schema = await service.identify_client_from_request(
            mock_request
        )
        assert client_id == "path_client"
        assert client_schema == "path_schema"
        print("✓ Path-based identification works")
    finally:
        service.config_service.get_client_schema = original_method


def test_config_utils():
    """Test configuration utility functions."""
    print("\nTesting configuration utilities...")

    # Mock request with configuration
    mock_request = Mock()
    mock_config = ClientConfiguration(
        client_id="test_client",
        missing_fields_to_look=["custom_field1", "custom_field2"],
    )
    mock_request.state.client_config = mock_config

    # Test getting config from request
    config = get_config_from_request(mock_request)
    assert config.client_id == "test_client"
    print("✓ Getting config from request works")

    # Test getting specific configuration values
    fields = get_missing_fields_to_look(mock_request)
    assert fields == ["custom_field1", "custom_field2"]
    print("✓ Getting specific config values works")


def test_service_singleton():
    """Test that the service singleton works."""
    print("\nTesting service singleton...")

    service1 = get_client_config_service()
    service2 = get_client_config_service()

    assert service1 is service2
    print("✓ Service singleton works")


async def test_default_configuration():
    """Test getting default configuration."""
    print("\nTesting default configuration...")

    service = get_client_config_service()
    default_config = service.get_default_configuration()

    assert default_config.root_path == "/referral-ai"
    assert default_config.server_name == "localhost"
    assert default_config.client_id is None
    print("✓ Default configuration works")


async def main():
    """Run all validation tests."""
    print("🚀 Starting client configuration system validation...\n")

    try:
        test_client_configuration()
        test_xml_parser()
        await test_client_identification()
        test_config_utils()
        test_service_singleton()
        await test_default_configuration()

        print("\n✅ All validation tests passed!")
        print("\n📋 Summary:")
        print("- Client configuration model: ✓")
        print("- XML parsing and merging: ✓")
        print("- Client identification: ✓")
        print("- Configuration utilities: ✓")
        print("- Service singleton: ✓")
        print("- Default configuration: ✓")

        print("\n🎉 Client configuration system is ready for use!")

    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
