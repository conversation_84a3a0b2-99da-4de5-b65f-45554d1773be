from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from services.client_config_service import get_client_config_service
from contextlib import asynccontextmanager

# Get default configuration for global database engine
_default_config = get_client_config_service().get_default_configuration()

engine = create_async_engine(
    f"postgresql+asyncpg://{_default_config.db_user}:{_default_config.db_pass}@{_default_config.db_host}:{_default_config.db_port}/{_default_config.db_name}",
    echo=False,
    future=True,
    pool_size=_default_config.db_pool_size,
    max_overflow=_default_config.max_overflow_connections,
    pool_timeout=_default_config.pool_timeout,
)

AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

Base = declarative_base()


@asynccontextmanager
async def get_db():
    """
    Yields an async SQLAlchemy session and ensures it's closed after use.
    """
    async with AsyncSessionLocal() as session:
        yield session
