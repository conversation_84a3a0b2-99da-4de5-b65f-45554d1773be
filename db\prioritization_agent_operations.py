from fastapi import status, Request
from utils.db_utils import get_model
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select, update, Numeric, cast
from sqlalchemy.exc import SQLAlchemyError
from exceptions import CustomError
from typing import List, Dict, Any, Optional
from models import PatientFacilityReferralFlag
from datetime import datetime, timezone
from utils.config_utils import get_config_from_request
from services.client_config_service import get_client_config_service


async def get_flagged_referrals_by_decline_rules(
    schema_name: str, db: AsyncSession, request: Optional[Request] = None
) -> List[Dict[str, Any]]:
    """
    Get referrals that meet any configured decline rule.

    Args:
        schema_name: Database schema name
        db: Database session
        request: FastAPI request object for client configuration

    Returns:
        List of flagged referrals.
    """
    # Get configuration (client-specific or default)
    if request:
        config = get_config_from_request(request)
    else:
        # Fallback to default configuration
        config = get_client_config_service().get_default_configuration()

    try:
        PatientFacilityReferralFlagModel = await get_model(
            PatientFacilityReferralFlag, schema_name
        )

        mandatory_filters = [
            PatientFacilityReferralFlagModel.refer_dcsn_priority_json.is_not(None),
            PatientFacilityReferralFlagModel.ai_status == None,
        ]

        optional_filters = []

        # Distance thresholds (miles to km)
        patient_dist_km = config.patient_to_facility_distance_threshold_miles * 1.60934
        source_dist_km = config.source_to_facility_distance_threshold_miles * 1.60934

        score_json = PatientFacilityReferralFlagModel.refer_dcsn_priority_json

        if config.disable_patient_to_facility_distance_decline != "true":
            optional_filters.append(
                cast(
                    score_json["score"]["patient_to_facility_distance"][
                        "distance_km"
                    ].astext,
                    Numeric,
                )
                > patient_dist_km
            )

        if config.disable_source_to_facility_distance_decline != "true":
            optional_filters.append(
                cast(
                    score_json["score"]["source_to_facility_distance"][
                        "distance_km"
                    ].astext,
                    Numeric,
                )
                > source_dist_km
            )

        if config.disable_shotgun_score_decline != "true":
            optional_filters.append(
                cast(score_json["score"]["shotgun"]["score"].astext, Numeric)
                < config.shotgun_score_threshold
            )

        if config.disable_referral_payer_decline != "true":
            optional_filters.append(
                cast(score_json["score"]["referral_payer"]["score"].astext, Numeric)
                < config.referral_payer_score_threshold
            )

        if config.disable_referral_source_decline != "true":
            optional_filters.append(
                cast(score_json["score"]["referral_sources"]["score"].astext, Numeric)
                < config.referral_source_score_threshold
            )

        if config.disable_referral_total_score_decline != "true":
            optional_filters.append(
                cast(score_json["total_score"].astext, Numeric)
                < config.referral_total_score_threshold
            )

        if not optional_filters:
            logging.info("All decline factors are disabled — skipping pending decline.")
            return []

        combined_filter = and_(*mandatory_filters, or_(*optional_filters))

        query = select(
            PatientFacilityReferralFlagModel.refer_id,
            PatientFacilityReferralFlagModel.refer_dcsn_id,
            PatientFacilityReferralFlagModel.refer_dcsn_priority_json,
            PatientFacilityReferralFlagModel.ai_status,
            PatientFacilityReferralFlagModel.ai_status_reason,
            PatientFacilityReferralFlagModel.ai_last_modified_at,
        ).where(combined_filter)

        result = await db.execute(query)
        rows = result.fetchall()
        columns = result.keys()

        flagged_referrals = [dict(zip(columns, row)) for row in rows]

        logging.info(
            f"Found {len(flagged_referrals)} referrals flagged by auto-decline rules."
        )

        return flagged_referrals

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def build_decline_reasons_json(
    referral_json: Dict[str, Any], request: Optional[Request] = None
) -> Dict[str, Any]:
    """
    Build a structured JSON response of decline reasons for a single referral.

    Args:
        referral_json (Dict[str, Any]): The 'refer_dcsn_priority_json' dict from a referral.
        request: FastAPI request object for client configuration

    Returns:
        Dict[str, Any]: A structured JSON object listing all matched decline criteria.
    """
    # Get configuration (client-specific or default)
    if request:
        config = get_config_from_request(request)
    else:
        # Fallback to default configuration
        config = get_client_config_service().get_default_configuration()

    reasons = []

    score = referral_json.get("score", {})
    total_score = referral_json.get("total_score")

    # Patient to facility distance
    if config.disable_patient_to_facility_distance_decline != "true":
        pdist = score.get("patient_to_facility_distance")
        if pdist:
            dist_km = pdist.get("distance_km")
            if (
                dist_km is not None
                and dist_km
                > config.patient_to_facility_distance_threshold_miles * 1.60934
            ):
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_PDIST",
                        "reasonObjKey": "scoring",
                        "scoring": {"patient_to_facility_distance": pdist},
                    }
                )

    # Source to facility distance
    if config.disable_source_to_facility_distance_decline != "true":
        sdist = score.get("source_to_facility_distance")
        if sdist:
            dist_km = sdist.get("distance_km")
            if (
                dist_km is not None
                and dist_km
                > config.source_to_facility_distance_threshold_miles * 1.60934
            ):
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_SDIST",
                        "reasonObjKey": "scoring",
                        "scoring": {"source_to_facility_distance": sdist},
                    }
                )

    # Shotgun score
    if config.disable_shotgun_score_decline != "true":
        shotgun = score.get("shotgun")
        if shotgun and shotgun.get("score") is not None:
            if shotgun["score"] < config.shotgun_score_threshold:
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_SHOTGUN",
                        "reasonObjKey": "scoring",
                        "scoring": {"shotgun": shotgun},
                    }
                )

    # Referral payer score
    if config.disable_referral_payer_decline != "true":
        payer = score.get("referral_payer")
        if payer and payer.get("score") is not None:
            if payer["score"] < config.referral_payer_score_threshold:
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_PAYER",
                        "reasonObjKey": "scoring",
                        "scoring": {"referral_payer": payer},
                    }
                )

    # Referral source score
    if config.disable_referral_source_decline != "true":
        source = score.get("referral_sources")
        if source and source.get("score") is not None:
            if source["score"] < config.referral_source_score_threshold:
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_SOURCE",
                        "reasonObjKey": "scoring",
                        "scoring": {"referral_sources": source},
                    }
                )

    # Total referral score
    if config.disable_referral_total_score_decline != "true":
        if (
            total_score is not None
            and total_score < config.referral_total_score_threshold
        ):
            reasons.append(
                {
                    "reasonCode": "SYSDEC_TOTAL_SCORE",
                    "reasonObjKey": "scoring",
                    "scoring": {"total_score": total_score},
                }
            )

    return {"reasons": reasons}


async def update_referral_status_based_on_scores_and_distance(
    referral: Dict[str, Any],
    schema_name: str,
    db: AsyncSession,
    request: Optional[Request] = None,
) -> Dict[str, Any]:
    """
    Update referral status based on scores and distance.

    Args:
        referral: The referral to update.
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.
        request: FastAPI request object for client configuration

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    reasons = build_decline_reasons_json(referral["refer_dcsn_priority_json"], request)
    refer_id = referral["refer_id"]
    refer_dcsn_id = referral["refer_dcsn_id"]

    try:
        PatientFacilityReferralFlagModel = await get_model(
            PatientFacilityReferralFlag, schema_name
        )

        stmt = (
            update(PatientFacilityReferralFlagModel)
            .where(
                and_(
                    PatientFacilityReferralFlagModel.refer_id == refer_id,
                    PatientFacilityReferralFlagModel.refer_dcsn_id == refer_dcsn_id,
                )
            )
            .values(
                ai_status="Pending_Decline",
                ai_status_reason=reasons,
                ai_last_modified_at=datetime.now(timezone.utc),
            )
            .execution_options(synchronize_session=False)
        )

        result = await db.execute(stmt)
        await db.commit()

        if result.rowcount > 0:
            logging.info(
                f"Updated ai_status to for refer_id: {refer_id}, refer_dcsn_id: {refer_dcsn_id}"
            )
            return {
                "refer_id": refer_id,
                "refer_dcsn_id": refer_dcsn_id,
                "status": "SUCCESS",
                "message": f"Updated ai_status for refer_id: {refer_id}, refer_dcsn_id: {refer_dcsn_id}",
            }
        else:
            return {
                "refer_id": refer_id,
                "refer_dcsn_id": refer_dcsn_id,
                "status": "NO_UPDATE",
                "message": f"No rows updated for refer_id: {refer_id}, refer_dcsn_id: {refer_dcsn_id}",
            }

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )
