import logging
import jwt
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from fastapi import Request, HTT<PERSON>Exception, status

from constants import JWT_SECRET_KEY, ENCRYPTION_ALGORITHM
from services.client_config_service import get_client_config_service


class ClientIdentificationService:
    """Service to identify clients from requests."""
    
    def __init__(self):
        self.config_service = get_client_config_service()
    
    async def identify_client_from_request(self, request: Request) -> Tuple[Optional[str], Optional[str]]:
        """
        Identify client from request using multiple methods.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Tuple of (client_id, client_schema) or (None, None) if not identified
        """
        # Method 1: Check for client ID in headers
        client_id = self._get_client_from_headers(request)
        if client_id:
            client_schema = await self.config_service.get_client_schema(client_id)
            return client_id, client_schema
        
        # Method 2: Extract from JWT token
        client_id, client_schema = await self._get_client_from_jwt(request)
        if client_id:
            return client_id, client_schema
        
        # Method 3: Extract from URL path (if client is part of the path)
        client_id = self._get_client_from_path(request)
        if client_id:
            client_schema = await self.config_service.get_client_schema(client_id)
            return client_id, client_schema
        
        # Method 4: Extract from query parameters
        client_id = self._get_client_from_query(request)
        if client_id:
            client_schema = await self.config_service.get_client_schema(client_id)
            return client_id, client_schema
        
        return None, None
    
    def _get_client_from_headers(self, request: Request) -> Optional[str]:
        """Extract client ID from request headers."""
        # Check common header names for client identification
        header_names = [
            'X-Client-ID',
            'X-Client-Id', 
            'Client-ID',
            'Client-Id',
            'X-Tenant-ID',
            'X-Tenant-Id'
        ]
        
        for header_name in header_names:
            client_id = request.headers.get(header_name)
            if client_id:
                logging.debug(f"Client identified from header {header_name}: {client_id}")
                return client_id.strip()
        
        return None
    
    async def _get_client_from_jwt(self, request: Request) -> Tuple[Optional[str], Optional[str]]:
        """Extract client information from JWT token."""
        try:
            # Get token from Authorization header
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return None, None
            
            token = auth_header.split(' ')[1]
            
            # Decode JWT token
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ENCRYPTION_ALGORITHM])
            
            # Extract client information from token
            client_id = payload.get('client_id') or payload.get('client') or payload.get('tenant_id')
            client_schema = payload.get('client_schema') or payload.get('schema')
            
            if client_id:
                logging.debug(f"Client identified from JWT: {client_id}")
                # If schema not in token, fetch from database
                if not client_schema:
                    client_schema = await self.config_service.get_client_schema(client_id)
                return client_id, client_schema
            
        except jwt.ExpiredSignatureError:
            logging.debug("JWT token expired during client identification")
        except jwt.InvalidTokenError:
            logging.debug("Invalid JWT token during client identification")
        except Exception as e:
            logging.debug(f"Error extracting client from JWT: {e}")
        
        return None, None
    
    def _get_client_from_path(self, request: Request) -> Optional[str]:
        """Extract client ID from URL path."""
        path = request.url.path
        
        # Common patterns for client in path:
        # /api/v1/client/{client_id}/...
        # /client/{client_id}/...
        # /{client_id}/api/...
        
        path_parts = [part for part in path.split('/') if part]
        
        # Pattern: /api/v1/client/{client_id}/...
        if len(path_parts) >= 4 and path_parts[2] == 'client':
            client_id = path_parts[3]
            logging.debug(f"Client identified from path pattern /api/v1/client/: {client_id}")
            return client_id
        
        # Pattern: /client/{client_id}/...
        if len(path_parts) >= 2 and path_parts[0] == 'client':
            client_id = path_parts[1]
            logging.debug(f"Client identified from path pattern /client/: {client_id}")
            return client_id
        
        # Pattern: /{client_id}/api/... (client as first path segment)
        # This is more risky, so we'll be conservative and only use it if the second part is 'api'
        if len(path_parts) >= 2 and path_parts[1] == 'api':
            client_id = path_parts[0]
            logging.debug(f"Client identified from path pattern /{client_id}/api/: {client_id}")
            return client_id
        
        return None
    
    def _get_client_from_query(self, request: Request) -> Optional[str]:
        """Extract client ID from query parameters."""
        query_params = request.query_params
        
        # Check common query parameter names
        param_names = ['client_id', 'client', 'tenant_id', 'tenant']
        
        for param_name in param_names:
            client_id = query_params.get(param_name)
            if client_id:
                logging.debug(f"Client identified from query param {param_name}: {client_id}")
                return client_id.strip()
        
        return None
    
    async def validate_client_access(self, client_id: str, request: Request) -> bool:
        """
        Validate that the identified client has access to the requested resource.
        
        Args:
            client_id: The identified client ID
            request: FastAPI request object
            
        Returns:
            True if client has access, False otherwise
        """
        try:
            # Check if client exists in the system
            client_exists = await self.config_service.validate_client_exists(client_id)
            if not client_exists:
                logging.warning(f"Client {client_id} does not exist in the system")
                return False
            
            # Additional access validation logic can be added here
            # For example, checking if client has access to specific endpoints
            
            return True
            
        except Exception as e:
            logging.error(f"Error validating client access for {client_id}: {e}")
            return False


# Global service instance
_client_identification_service: Optional[ClientIdentificationService] = None


def get_client_identification_service() -> ClientIdentificationService:
    """Get the global client identification service instance."""
    global _client_identification_service
    if _client_identification_service is None:
        _client_identification_service = ClientIdentificationService()
    return _client_identification_service
