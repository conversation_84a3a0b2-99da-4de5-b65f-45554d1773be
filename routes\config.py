from fastapi import APIRouter, Request, Response
from ratelimit import limiter
from constants import *


config_router = APIRouter()


def get_field_mappings_xml():
    """Generate the FieldMappings XML section based on current configuration."""
    field_mappings = []

    for column, prompt, label in zip(
        MISSING_FIELDS_TO_LOOK, FIELDS_MAPPING_PROMPT, MISSING_FIELDS_LABELS
    ):
        is_high_priority = column in HIGH_PRIORITY_FIELDS
        field_mappings.append(
            f"""
            <Field>
                <Column>{column}</Column>
                <Prompt>{prompt}</Prompt>
                <Label>{label}</Label>
                <HighPriority>{'true' if is_high_priority else 'false'}</HighPriority>
            </Field>"""
        )

    return "\n".join(field_mappings)


@config_router.get("/v1/config", tags=["Configuration"])
@limiter.limit("5/minute")
async def get_client_config(request: Request):
    """
    Returns the referralAIClientConfig XML with current configuration values.

    This endpoint provides the complete configuration for the Referral AI system
    including authentication, database, workflow agents, and scheduler settings.
    """
    field_mappings_xml = get_field_mappings_xml()
    xml_content = f"""
<referralAIClientConfig>
  <AIConfiguration>

    <RootPath>{ROOT_PATH}</RootPath>
    <ServerName>{SERVER_NAME}</ServerName>

    <Authentication>
      <JwtSecretKey></JwtSecretKey>
      <EncryptionAlgorithm>{ENCRYPTION_ALGORITHM}</EncryptionAlgorithm>
      <AccessTokenExpireMinutes>{ACCESS_TOKEN_EXPIRE_MINUTES}</AccessTokenExpireMinutes>
      <RefreshTokenExpireMinutes>{REFRESH_TOKEN_EXPIRE_MINUTES}</RefreshTokenExpireMinutes>
      <AuthCredentials></AuthCredentials>
      <AuthKeysDelimiter></AuthKeysDelimiter>
      <SwaggerBasicAuthKey></SwaggerBasicAuthKey>
    </Authentication>

    <Database>
      <Host></Host>
      <Port></Port>
      <Name></Name>
      <User></User>
      <Password></Password>
      <PoolSize>{DB_POOL_SIZE}</PoolSize>
      <MaxOverflowConnections>{MAX_OVERFLOW_CONNECTIONS}</MaxOverflowConnections>
      <PoolTimeout>{POOL_TIMEOUT}</PoolTimeout>
    </Database>

    <Workers>
      <ClientWorkers>{CLIENT_WORKERS}</ClientWorkers>
      <ReferralProcessingWorkers>{REFERRAL_PROCESSING_WORKERS}</ReferralProcessingWorkers>
    </Workers>

    <PDFPlus>
      <BaseUrl>{PDFPLUS_URL}</BaseUrl>
      <ExtractFieldsEndpoint>{PDFPLUS_EXTRACT_FIELDS_ENDPOINT}</ExtractFieldsEndpoint>
      <AuthKey></AuthKey>
    </PDFPlus>

    <WorkflowConfigurations>
      <MissingInfoAgent>
        <DisableAgent>{DISABLE_MISSING_INFO_AGENT}</DisableAgent>
        <DisableNewReferralWorkflow>{DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW}</DisableNewReferralWorkflow>
        <DisableRetriggerWorkflow>{DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW}</DisableRetriggerWorkflow>
        <FieldMappings>
{field_mappings_xml}
        </FieldMappings>
      </MissingInfoAgent>

      <TaskWorkflowAgent>
        <DisableAgent>{DISABLE_TASK_AND_WORKFLOW_AGENT}</DisableAgent>
        <DisableTaskCreationWorkflow>{DISABLE_TASK_CREATION_WORKFLOW}</DisableTaskCreationWorkflow>
        <TaskSubject>{TASK_SUBJECT}</TaskSubject>
        <Status>{NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS}</Status>
        <Comment>{COMMENT}</Comment>
        <CommentType>{COMMENT_TYPE}</CommentType>
        <CreatedByUsername>{CREATED_BY_USERNAME}</CreatedByUsername>
        <CreatedByUserId>{CREATED_BY_USER_ID}</CreatedByUserId>
        <TaskCreationWaitingMinutes>{TASK_CREATION_WAITING_MINUTES}</TaskCreationWaitingMinutes>
      </TaskWorkflowAgent>

      <PrioritizationAgent>
        <DisableAgent>{DISABLE_PRIORITIZATION_AGENT}</DisableAgent>
        <DisableAutoDeclineWorkflow>{DISABLE_AUTO_DECLINE_WORKFLOW}</DisableAutoDeclineWorkflow>
        <DisablePatientToFacilityDistanceDecline>{DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE}</DisablePatientToFacilityDistanceDecline>
        <DisableSourceToFacilityDistanceDecline>{DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE}</DisableSourceToFacilityDistanceDecline>
        <DisableShotgunScoreDecline>{DISABLE_SHOTGUN_SCORE_DECLINE}</DisableShotgunScoreDecline>
        <DisableReferralPayerDecline>{DISABLE_REFERRAL_PAYER_DECLINE}</DisableReferralPayerDecline>
        <DisableReferralSourceDecline>{DISABLE_REFERRAL_SOURCE_DECLINE}</DisableReferralSourceDecline>
        <DisableReferralTotalScoreDecline>{DISABLE_REFERRAL_TOTAL_SCORE_DECLINE}</DisableReferralTotalScoreDecline>
        <AutoDeclineCriteria>
            <ReferralScores>
                <SourceToFacilityDistanceMiles>{SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES}</SourceToFacilityDistanceMiles>
                <PatientToFacilityDistanceMiles>{PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES}</PatientToFacilityDistanceMiles>
                <ShotgunScore>{SHOTGUN_SCORE_THRESHOLD}</ShotgunScore>
                <ReferralPayerScore>{REFERRAL_PAYER_SCORE_THRESHOLD}</ReferralPayerScore>
                <ReferralSourceScore>{REFERRAL_SOURCE_SCORE_THRESHOLD}</ReferralSourceScore>
                <ReferralTotalScore>{REFERRAL_TOTAL_SCORE_THRESHOLD}</ReferralTotalScore>
            </ReferralScores>
        </AutoDeclineCriteria>
      </PrioritizationAgent>

      <ErrorMonitoring>
        <DisableMonitoring>{DISABLE_ERROR_MONITORING}</DisableMonitoring>
        <AlertIntervalMinutes>{ERROR_ALERT_INTERVAL_MINUTES}</AlertIntervalMinutes>
        <AlertSubject>{ERROR_ALERT_SUBJECT}</AlertSubject>
        <Email>
            <AuthUser></AuthUser>
            <AppPassword></AppPassword>
            <ToUser></ToUser>
            <FromUser></FromUser>
            <EnablePostfix></EnablePostfix>
        </Email>
      </ErrorMonitoring>
    </WorkflowConfigurations>

    <Scheduler>
      <DisableInternalScheduler>{DISABLE_INTERNAL_SCHEDULER}</DisableInternalScheduler>
      <ErrorMonitoringIntervalSeconds>{ERROR_MONITORING_INTERVAL_SECONDS}</ErrorMonitoringIntervalSeconds>
      <MissingInfoAgentIntervalSeconds>{MISSING_INFO_AGENT_INTERVAL_SECONDS}</MissingInfoAgentIntervalSeconds>
      <TaskWorkflowAgentIntervalSeconds>{TASK_WORKFLOW_AGENT_INTERVAL_SECONDS}</TaskWorkflowAgentIntervalSeconds>
      <PrioritizationAgentIntervalSeconds>{PRIORITIZATION_AGENT_INTERVAL_SECONDS}</PrioritizationAgentIntervalSeconds>
    </Scheduler>

  </AIConfiguration>
</referralAIClientConfig>"""

    return Response(
        content=xml_content,
        media_type="application/xml",
    )
