from fastapi import APIRouter, Request, Response
from ratelimit import limiter
from utils.config_utils import get_config_from_request, get_missing_info_config


config_router = APIRouter()


def get_field_mappings_xml(request: Request):
    """Generate the FieldMappings XML section based on current configuration."""
    field_mappings = []

    # Get missing info configuration from request
    missing_info_config = get_missing_info_config(request)

    missing_fields_to_look = missing_info_config.get("MISSING_FIELDS_TO_LOOK", [])
    fields_mapping_prompt = missing_info_config.get("FIELDS_MAPPING_PROMPT", [])
    missing_fields_labels = missing_info_config.get("MISSING_FIELDS_LABELS", [])
    high_priority_fields = missing_info_config.get("HIGH_PRIORITY_FIELDS", [])

    for column, prompt, label in zip(
        missing_fields_to_look, fields_mapping_prompt, missing_fields_labels
    ):
        is_high_priority = column in high_priority_fields
        field_mappings.append(
            f"""
            <Field>
                <Column>{column}</Column>
                <Prompt>{prompt}</Prompt>
                <Label>{label}</Label>
                <HighPriority>{'true' if is_high_priority else 'false'}</HighPriority>
            </Field>"""
        )

    return "\n".join(field_mappings)


@config_router.get("/v1/config", tags=["Configuration"])
@limiter.limit("5/minute")
async def get_client_config(request: Request):
    """
    Returns the referralAIClientConfig XML with current configuration values.

    This endpoint provides the complete configuration for the Referral AI system
    including authentication, database, workflow agents, and scheduler settings.
    """
    # Get client-specific configuration
    config = get_config_from_request(request)

    field_mappings_xml = get_field_mappings_xml(request)
    xml_content = f"""
<referralAIClientConfig>
  <AIConfiguration>

    <RootPath>{config.get('ROOT_PATH', '')}</RootPath>
    <ServerName>{config.get('SERVER_NAME', '')}</ServerName>

    <Authentication>
      <JwtSecretKey></JwtSecretKey>
      <EncryptionAlgorithm>{config.get('ENCRYPTION_ALGORITHM', '')}</EncryptionAlgorithm>
      <AccessTokenExpireMinutes>{config.get('ACCESS_TOKEN_EXPIRE_MINUTES', 0)}</AccessTokenExpireMinutes>
      <RefreshTokenExpireMinutes>{config.get('REFRESH_TOKEN_EXPIRE_MINUTES', 0)}</RefreshTokenExpireMinutes>
      <AuthCredentials></AuthCredentials>
      <AuthKeysDelimiter></AuthKeysDelimiter>
      <SwaggerBasicAuthKey></SwaggerBasicAuthKey>
    </Authentication>

    <Database>
      <Host></Host>
      <Port></Port>
      <Name></Name>
      <User></User>
      <Password></Password>
      <PoolSize>{config.get('DB_POOL_SIZE', 0)}</PoolSize>
      <MaxOverflowConnections>{config.get('MAX_OVERFLOW_CONNECTIONS', 0)}</MaxOverflowConnections>
      <PoolTimeout>{config.get('POOL_TIMEOUT', 0)}</PoolTimeout>
    </Database>

    <Workers>
      <ClientWorkers>{config.get('CLIENT_WORKERS', 0)}</ClientWorkers>
      <ReferralProcessingWorkers>{config.get('REFERRAL_PROCESSING_WORKERS', 0)}</ReferralProcessingWorkers>
    </Workers>

    <PDFPlus>
      <BaseUrl>{config.get('PDFPLUS_URL', '')}</BaseUrl>
      <ExtractFieldsEndpoint>{config.get('PDFPLUS_EXTRACT_FIELDS_ENDPOINT', '')}</ExtractFieldsEndpoint>
      <AuthKey></AuthKey>
    </PDFPlus>

    <WorkflowConfigurations>
      <MissingInfoAgent>
        <DisableAgent>{config.get('DISABLE_MISSING_INFO_AGENT', False)}</DisableAgent>
        <DisableNewReferralWorkflow>{config.get('DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW', False)}</DisableNewReferralWorkflow>
        <DisableRetriggerWorkflow>{config.get('DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW', False)}</DisableRetriggerWorkflow>
        <FieldMappings>
{field_mappings_xml}
        </FieldMappings>
      </MissingInfoAgent>

      <TaskWorkflowAgent>
        <DisableAgent>{config.get('DISABLE_TASK_AND_WORKFLOW_AGENT', False)}</DisableAgent>
        <DisableTaskCreationWorkflow>{config.get('DISABLE_TASK_CREATION_WORKFLOW', False)}</DisableTaskCreationWorkflow>
        <TaskSubject>{config.get('TASK_SUBJECT', '')}</TaskSubject>
        <Status>{config.get('NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS', '')}</Status>
        <Comment>{config.get('COMMENT', '')}</Comment>
        <CommentType>{config.get('COMMENT_TYPE', '')}</CommentType>
        <CreatedByUsername>{config.get('CREATED_BY_USERNAME', '')}</CreatedByUsername>
        <CreatedByUserId>{config.get('CREATED_BY_USER_ID', 0)}</CreatedByUserId>
        <TaskCreationWaitingMinutes>{config.get('TASK_CREATION_WAITING_MINUTES', 0)}</TaskCreationWaitingMinutes>
      </TaskWorkflowAgent>

      <PrioritizationAgent>
        <DisableAgent>{config.get('DISABLE_PRIORITIZATION_AGENT', False)}</DisableAgent>
        <DisableAutoDeclineWorkflow>{config.get('DISABLE_AUTO_DECLINE_WORKFLOW', False)}</DisableAutoDeclineWorkflow>
        <DisablePatientToFacilityDistanceDecline>{config.get('DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE', False)}</DisablePatientToFacilityDistanceDecline>
        <DisableSourceToFacilityDistanceDecline>{config.get('DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE', False)}</DisableSourceToFacilityDistanceDecline>
        <DisableShotgunScoreDecline>{config.get('DISABLE_SHOTGUN_SCORE_DECLINE', False)}</DisableShotgunScoreDecline>
        <DisableReferralPayerDecline>{config.get('DISABLE_REFERRAL_PAYER_DECLINE', False)}</DisableReferralPayerDecline>
        <DisableReferralSourceDecline>{config.get('DISABLE_REFERRAL_SOURCE_DECLINE', False)}</DisableReferralSourceDecline>
        <DisableReferralTotalScoreDecline>{config.get('DISABLE_REFERRAL_TOTAL_SCORE_DECLINE', False)}</DisableReferralTotalScoreDecline>
        <AutoDeclineCriteria>
            <ReferralScores>
                <SourceToFacilityDistanceMiles>{config.get('SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES', 0)}</SourceToFacilityDistanceMiles>
                <PatientToFacilityDistanceMiles>{config.get('PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES', 0)}</PatientToFacilityDistanceMiles>
                <ShotgunScore>{config.get('SHOTGUN_SCORE_THRESHOLD', 0)}</ShotgunScore>
                <ReferralPayerScore>{config.get('REFERRAL_PAYER_SCORE_THRESHOLD', 0)}</ReferralPayerScore>
                <ReferralSourceScore>{config.get('REFERRAL_SOURCE_SCORE_THRESHOLD', 0)}</ReferralSourceScore>
                <ReferralTotalScore>{config.get('REFERRAL_TOTAL_SCORE_THRESHOLD', 0)}</ReferralTotalScore>
            </ReferralScores>
        </AutoDeclineCriteria>
      </PrioritizationAgent>

      <ErrorMonitoring>
        <DisableMonitoring>{config.get('DISABLE_ERROR_MONITORING', False)}</DisableMonitoring>
        <AlertIntervalMinutes>{config.get('ERROR_ALERT_INTERVAL_MINUTES', 0)}</AlertIntervalMinutes>
        <AlertSubject>{config.get('ERROR_ALERT_SUBJECT', '')}</AlertSubject>
        <Email>
            <AuthUser></AuthUser>
            <AppPassword></AppPassword>
            <ToUser></ToUser>
            <FromUser></FromUser>
            <EnablePostfix></EnablePostfix>
        </Email>
      </ErrorMonitoring>
    </WorkflowConfigurations>

    <Scheduler>
      <DisableInternalScheduler>{config.get('DISABLE_INTERNAL_SCHEDULER', False)}</DisableInternalScheduler>
      <ErrorMonitoringIntervalSeconds>{config.get('ERROR_MONITORING_INTERVAL_SECONDS', 0)}</ErrorMonitoringIntervalSeconds>
      <MissingInfoAgentIntervalSeconds>{config.get('MISSING_INFO_AGENT_INTERVAL_SECONDS', 0)}</MissingInfoAgentIntervalSeconds>
      <TaskWorkflowAgentIntervalSeconds>{config.get('TASK_WORKFLOW_AGENT_INTERVAL_SECONDS', 0)}</TaskWorkflowAgentIntervalSeconds>
      <PrioritizationAgentIntervalSeconds>{config.get('PRIORITIZATION_AGENT_INTERVAL_SECONDS', 0)}</PrioritizationAgentIntervalSeconds>
    </Scheduler>

  </AIConfiguration>
</referralAIClientConfig>"""

    return Response(
        content=xml_content,
        media_type="application/xml",
    )
