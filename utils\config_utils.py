from typing import Optional, Any, List
from fastapi import Request
from contextvars import <PERSON><PERSON><PERSON><PERSON>
import logging

from models.client_config import ClientConfiguration
from services.client_config_service import get_client_config_service

# Context variable to store current request's client configuration
_current_client_config: ContextVar[Optional[ClientConfiguration]] = ContextVar(
    'current_client_config', default=None
)


def set_current_client_config(config: ClientConfiguration) -> None:
    """Set the current client configuration in context."""
    _current_client_config.set(config)


def get_current_client_config() -> Optional[ClientConfiguration]:
    """Get the current client configuration from context."""
    return _current_client_config.get()


def get_config_from_request(request: Request) -> ClientConfiguration:
    """
    Get client configuration from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        ClientConfiguration object
    """
    if hasattr(request.state, 'client_config') and request.state.client_config:
        return request.state.client_config
    
    # Fallback to default configuration
    logging.warning("No client configuration found in request state, using defaults")
    return get_client_config_service().get_default_configuration()


def get_client_id_from_request(request: Request) -> Optional[str]:
    """
    Get client ID from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Client ID or None if not available
    """
    return getattr(request.state, 'client_id', None)


def get_client_schema_from_request(request: Request) -> Optional[str]:
    """
    Get client schema from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Client schema or None if not available
    """
    return getattr(request.state, 'client_schema', None)


# Configuration access functions that can be used throughout the application

def get_config_value(request: Request, key: str, default: Any = None) -> Any:
    """
    Get a configuration value by key from the client configuration.
    
    Args:
        request: FastAPI request object
        key: Configuration key name
        default: Default value if key not found
        
    Returns:
        Configuration value or default
    """
    config = get_config_from_request(request)
    return getattr(config, key, default)


# Specific configuration getters for commonly used values

def get_db_config(request: Request) -> dict:
    """Get database configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'host': config.db_host,
        'port': config.db_port,
        'name': config.db_name,
        'user': config.db_user,
        'password': config.db_pass,
        'pool_size': config.db_pool_size,
        'max_overflow': config.max_overflow_connections,
        'pool_timeout': config.pool_timeout
    }


def get_auth_config(request: Request) -> dict:
    """Get authentication configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'jwt_secret_key': config.jwt_secret_key,
        'encryption_algorithm': config.encryption_algorithm,
        'access_token_expire_minutes': config.access_token_expire_minutes,
        'refresh_token_expire_minutes': config.refresh_token_expire_minutes,
        'auth_credentials': config.auth_credentials,
        'auth_keys_delimiter': config.auth_keys_delimiter,
        'swagger_basic_auth_key': config.swagger_basic_auth_key
    }


def get_missing_info_agent_config(request: Request) -> dict:
    """Get missing info agent configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'disable_agent': config.disable_missing_info_agent,
        'disable_new_referral_workflow': config.disable_missing_info_new_referral_workflow,
        'disable_retrigger_workflow': config.disable_missing_info_retrigger_workflow,
        'missing_fields_to_look': config.missing_fields_to_look,
        'fields_mapping_prompt': config.fields_mapping_prompt,
        'missing_fields_labels': config.missing_fields_labels,
        'high_priority_fields': config.high_priority_fields
    }


def get_task_workflow_config(request: Request) -> dict:
    """Get task workflow configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'task_subject': config.task_subject,
        'new_comment_task_status': config.new_comment_task_status_for_missing_fields,
        'comment': config.comment,
        'comment_type': config.comment_type,
        'created_by_username': config.created_by_username,
        'created_by_user_id': config.created_by_user_id,
        'task_creation_waiting_minutes': config.task_creation_waiting_minutes,
        'disable_task_creation_workflow': config.disable_task_creation_workflow,
        'disable_task_and_workflow_agent': config.disable_task_and_workflow_agent
    }


def get_prioritization_agent_config(request: Request) -> dict:
    """Get prioritization agent configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'disable_prioritization_agent': config.disable_prioritization_agent,
        'disable_auto_decline_workflow': config.disable_auto_decline_workflow,
        'disable_patient_to_facility_distance_decline': config.disable_patient_to_facility_distance_decline,
        'disable_source_to_facility_distance_decline': config.disable_source_to_facility_distance_decline,
        'disable_shotgun_score_decline': config.disable_shotgun_score_decline,
        'disable_referral_payer_decline': config.disable_referral_payer_decline,
        'disable_referral_source_decline': config.disable_referral_source_decline
    }


def get_pdfplus_config(request: Request) -> dict:
    """Get PDFPlus configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'base_url': config.pdfplus_url,
        'extract_fields_endpoint': config.pdfplus_extract_fields_endpoint,
        'auth_key': config.pdfplus_auth_key
    }


def get_workers_config(request: Request) -> dict:
    """Get workers configuration for the current client."""
    config = get_config_from_request(request)
    return {
        'client_workers': config.client_workers,
        'referral_processing_workers': config.referral_processing_workers
    }


# Convenience functions for backward compatibility with existing code

def get_missing_fields_to_look(request: Request) -> List[str]:
    """Get missing fields to look for the current client."""
    return get_config_from_request(request).missing_fields_to_look


def get_fields_mapping_prompt(request: Request) -> List[str]:
    """Get fields mapping prompt for the current client."""
    return get_config_from_request(request).fields_mapping_prompt


def get_missing_fields_labels(request: Request) -> List[str]:
    """Get missing fields labels for the current client."""
    return get_config_from_request(request).missing_fields_labels


def get_high_priority_fields(request: Request) -> List[str]:
    """Get high priority fields for the current client."""
    return get_config_from_request(request).high_priority_fields


def is_missing_info_agent_disabled(request: Request) -> bool:
    """Check if missing info agent is disabled for the current client."""
    return get_config_from_request(request).disable_missing_info_agent.lower() == 'true'


def is_task_creation_workflow_disabled(request: Request) -> bool:
    """Check if task creation workflow is disabled for the current client."""
    return get_config_from_request(request).disable_task_creation_workflow.lower() == 'true'


def is_prioritization_agent_disabled(request: Request) -> bool:
    """Check if prioritization agent is disabled for the current client."""
    return get_config_from_request(request).disable_prioritization_agent.lower() == 'true'


# Context manager for setting client configuration in non-request contexts
class ClientConfigContext:
    """Context manager to set client configuration for non-request contexts."""
    
    def __init__(self, client_config: ClientConfiguration):
        self.client_config = client_config
        self.previous_config = None
    
    def __enter__(self):
        self.previous_config = get_current_client_config()
        set_current_client_config(self.client_config)
        return self.client_config
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        set_current_client_config(self.previous_config)


def get_config_value_from_context(key: str, default: Any = None) -> Any:
    """
    Get configuration value from context (for non-request contexts).
    
    Args:
        key: Configuration key name
        default: Default value if key not found
        
    Returns:
        Configuration value or default
    """
    config = get_current_client_config()
    if config:
        return getattr(config, key, default)
    
    # Fallback to default configuration
    default_config = get_client_config_service().get_default_configuration()
    return getattr(default_config, key, default)
