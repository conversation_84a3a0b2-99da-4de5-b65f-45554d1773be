import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from fastapi import Request
from fastapi.testclient import Test<PERSON>lient

from models.client_config import ClientConfiguration, ClientConfigurationParser
from services.client_config_service import ClientConfigurationService
from middleware.client_identification import ClientIdentificationService
from utils.config_utils import get_config_from_request, get_missing_fields_to_look


class TestClientConfiguration:
    """Test client configuration data model."""
    
    def test_client_configuration_defaults(self):
        """Test that ClientConfiguration uses proper defaults."""
        config = ClientConfiguration()
        
        assert config.root_path == "/referral-ai"
        assert config.server_name == "localhost"
        assert config.client_id is None
        assert config.client_schema is None
    
    def test_client_configuration_to_dict(self):
        """Test converting configuration to dictionary."""
        config = ClientConfiguration(client_id="test_client", client_schema="test_schema")
        config_dict = config.to_dict()
        
        assert config_dict["client_id"] == "test_client"
        assert config_dict["client_schema"] == "test_schema"
        assert "root_path" in config_dict
    
    def test_client_configuration_from_dict(self):
        """Test creating configuration from dictionary."""
        data = {
            "client_id": "test_client",
            "client_schema": "test_schema",
            "root_path": "/custom-path"
        }
        
        config = ClientConfiguration.from_dict(data)
        
        assert config.client_id == "test_client"
        assert config.client_schema == "test_schema"
        assert config.root_path == "/custom-path"


class TestClientConfigurationParser:
    """Test XML configuration parsing."""
    
    def test_parse_basic_xml_config(self):
        """Test parsing basic XML configuration."""
        xml_content = """
        <referralAIClientConfig>
            <AIConfiguration>
                <RootPath>/custom-path</RootPath>
                <ServerName>custom-server</ServerName>
                <Authentication>
                    <EncryptionAlgorithm>HS256</EncryptionAlgorithm>
                    <AccessTokenExpireMinutes>120</AccessTokenExpireMinutes>
                </Authentication>
                <Database>
                    <Host>custom-host</Host>
                    <Port>5433</Port>
                    <PoolSize>10</PoolSize>
                </Database>
            </AIConfiguration>
        </referralAIClientConfig>
        """
        
        parser = ClientConfigurationParser()
        config_data = parser.parse_xml_config(xml_content)
        
        assert config_data["root_path"] == "/custom-path"
        assert config_data["server_name"] == "custom-server"
        assert config_data["encryption_algorithm"] == "HS256"
        assert config_data["access_token_expire_minutes"] == 120
        assert config_data["db_host"] == "custom-host"
        assert config_data["db_port"] == "5433"
        assert config_data["db_pool_size"] == 10
    
    def test_parse_missing_info_agent_config(self):
        """Test parsing missing info agent configuration."""
        xml_content = """
        <referralAIClientConfig>
            <AIConfiguration>
                <WorkflowConfigurations>
                    <MissingInfoAgent>
                        <DisableAgent>false</DisableAgent>
                        <DisableNewReferralWorkflow>false</DisableNewReferralWorkflow>
                        <FieldMappings>
                            <Field>
                                <Column>ssn</Column>
                                <Prompt>Social Security Number</Prompt>
                                <Label>SSN</Label>
                                <HighPriority>true</HighPriority>
                            </Field>
                            <Field>
                                <Column>address</Column>
                                <Prompt>Patient Address</Prompt>
                                <Label>Address</Label>
                                <HighPriority>false</HighPriority>
                            </Field>
                        </FieldMappings>
                    </MissingInfoAgent>
                </WorkflowConfigurations>
            </AIConfiguration>
        </referralAIClientConfig>
        """
        
        parser = ClientConfigurationParser()
        config_data = parser.parse_xml_config(xml_content)
        
        assert config_data["disable_missing_info_agent"] == "false"
        assert config_data["disable_missing_info_new_referral_workflow"] == "false"
        assert config_data["missing_fields_to_look"] == ["ssn", "address"]
        assert config_data["fields_mapping_prompt"] == ["Social Security Number", "Patient Address"]
        assert config_data["missing_fields_labels"] == ["SSN", "Address"]
        assert config_data["high_priority_fields"] == ["ssn"]
    
    def test_merge_with_defaults(self):
        """Test merging client config with defaults."""
        client_config = {
            "root_path": "/custom-path",
            "db_host": "custom-host",
            "missing_fields_to_look": ["custom_field"]
        }
        
        parser = ClientConfigurationParser()
        merged_config = parser.merge_with_defaults(client_config)
        
        # Should have custom values
        assert merged_config.root_path == "/custom-path"
        assert merged_config.db_host == "custom-host"
        assert merged_config.missing_fields_to_look == ["custom_field"]
        
        # Should have default values for non-specified fields
        assert merged_config.server_name == "localhost"  # default
        assert merged_config.db_port == "5432"  # default


class TestClientIdentificationService:
    """Test client identification from requests."""
    
    @pytest.fixture
    def mock_request(self):
        """Create a mock request object."""
        request = Mock(spec=Request)
        request.headers = {}
        request.url.path = "/api/v1/test"
        request.query_params = {}
        return request
    
    @pytest.mark.asyncio
    async def test_identify_client_from_headers(self, mock_request):
        """Test client identification from headers."""
        mock_request.headers = {"X-Client-ID": "test_client"}
        
        service = ClientIdentificationService()
        
        with patch.object(service.config_service, 'get_client_schema', return_value="test_schema"):
            client_id, client_schema = await service.identify_client_from_request(mock_request)
        
        assert client_id == "test_client"
        assert client_schema == "test_schema"
    
    @pytest.mark.asyncio
    async def test_identify_client_from_path(self, mock_request):
        """Test client identification from URL path."""
        mock_request.url.path = "/api/v1/client/test_client/data"
        
        service = ClientIdentificationService()
        
        with patch.object(service.config_service, 'get_client_schema', return_value="test_schema"):
            client_id, client_schema = await service.identify_client_from_request(mock_request)
        
        assert client_id == "test_client"
        assert client_schema == "test_schema"
    
    @pytest.mark.asyncio
    async def test_identify_client_from_query(self, mock_request):
        """Test client identification from query parameters."""
        mock_request.query_params = {"client_id": "test_client"}
        
        service = ClientIdentificationService()
        
        with patch.object(service.config_service, 'get_client_schema', return_value="test_schema"):
            client_id, client_schema = await service.identify_client_from_request(mock_request)
        
        assert client_id == "test_client"
        assert client_schema == "test_schema"


class TestClientConfigurationService:
    """Test client configuration service."""
    
    @pytest.mark.asyncio
    async def test_get_client_configuration_with_cache(self):
        """Test getting client configuration with caching."""
        service = ClientConfigurationService()
        
        # Mock database fetch
        with patch.object(service, '_fetch_client_config_from_db', return_value=None):
            config1 = await service.get_client_configuration("test_client")
            config2 = await service.get_client_configuration("test_client")
        
        # Should return same instance from cache
        assert config1.client_id == "test_client"
        assert config2.client_id == "test_client"
    
    @pytest.mark.asyncio
    async def test_refresh_client_configuration(self):
        """Test refreshing client configuration."""
        service = ClientConfigurationService()
        
        # First call
        with patch.object(service, '_fetch_client_config_from_db', return_value=None):
            config1 = await service.get_client_configuration("test_client")
        
        # Refresh should invalidate cache and fetch again
        with patch.object(service, '_fetch_client_config_from_db', return_value=None):
            config2 = await service.refresh_client_configuration("test_client")
        
        assert config1.client_id == config2.client_id


class TestConfigUtils:
    """Test configuration utility functions."""
    
    def test_get_config_from_request(self):
        """Test getting configuration from request state."""
        mock_request = Mock(spec=Request)
        mock_config = ClientConfiguration(client_id="test_client")
        mock_request.state.client_config = mock_config
        
        config = get_config_from_request(mock_request)
        
        assert config.client_id == "test_client"
    
    def test_get_missing_fields_to_look(self):
        """Test getting missing fields configuration."""
        mock_request = Mock(spec=Request)
        mock_config = ClientConfiguration(missing_fields_to_look=["custom_field1", "custom_field2"])
        mock_request.state.client_config = mock_config
        
        fields = get_missing_fields_to_look(mock_request)
        
        assert fields == ["custom_field1", "custom_field2"]


if __name__ == "__main__":
    pytest.main([__file__])
