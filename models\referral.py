from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Boolean,
    Text,
    Float,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from sqlalchemy.ext.mutable import MutableDict
from db import Base


class PatientReferralAI(Base):
    """SQLAlchemy model for gs_patient_referral_ai table."""

    __tablename__ = "gs_patient_referral_ai"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    refer_id = Column(Integer, primary_key=True)
    patient_id = Column(Integer, nullable=False)
    res_fields_json = Column(MutableDict.as_mutable(JSONB))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    require_task = Column(Boolean, default=False)
    task_created = Column(Boolean, default=False)

    def __repr__(self):
        return f"<PatientReferralAI(refer_id={self.refer_id}, patient_id={self.patient_id})>"


class PatientReferral(Base):
    """
    SQLAlchemy model for gs_patient_referral table.
    """

    __tablename__ = "gs_patient_referral"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    refer_id = Column(Integer, primary_key=True)
    patient_id = Column(Integer, nullable=False)
    res_fn = Column(String, nullable=True)
    res_mn = Column(String, nullable=True)
    res_ln = Column(String, nullable=True)
    res_sfx = Column(String, nullable=True)
    res_bdt = Column(String, nullable=True)
    resmce_id = Column(String, nullable=True)
    res_ssn = Column(String, nullable=True)
    res_pn = Column(String, nullable=True)
    resgdr = Column(String, nullable=True)
    resmarital = Column(String, nullable=True)
    res_height = Column(Float, nullable=True)
    res_weight = Column(Float, nullable=True)
    res_acsz = Column(String, nullable=True)
    res_1addr = Column(String, nullable=True)
    res_2addr = Column(String, nullable=True)
    res_addr = Column(String, nullable=True)
    res_city = Column(String, nullable=True)
    res_st = Column(String, nullable=True)
    res_zip = Column(String, nullable=True)
    res_cnty = Column(String, nullable=True)
    res_ph = Column(String, nullable=True)
    res_lnfmi = Column(String, nullable=True)
    resdiag1_id = Column(String, nullable=True)
    resdiag1_desc = Column(String, nullable=True)
    resdiag2_id = Column(String, nullable=True)
    resdiag2_desc = Column(String, nullable=True)

    """
    Note: There are additional columns in the table, but they are not
    included as they are not used atleast for now. Keeping all the
    potential columns for missing fields in the table.
    """

    def __repr__(self):
        return f"<PatientReferral(refer_id={self.refer_id}, patient_id={self.patient_id}, res_fn={self.res_fn}, res_mn={self.res_mn}, res_ln={self.res_ln}, res_sfx={self.res_sfx}, res_bdt={self.res_bdt}, resmce_id={self.resmce_id}, res_ssn={self.res_ssn}, res_pn={self.res_pn}, resgdr={self.resgdr}, resmarital={self.resmarital}, res_height={self.res_height}, res_weight={self.res_weight}, res_acsz={self.res_acsz}, res_1addr={self.res_1addr}, res_2addr={self.res_2addr}, res_addr={self.res_addr}, res_city={self.res_city}, res_st={self.res_st}, res_zip={self.res_zip}, res_cnty={self.res_cnty}, res_ph={self.res_ph}, res_lnfmi={self.res_lnfmi}, resdiag1_id={self.resdiag1_id}, resdiag1_desc={self.resdiag1_desc}, resdiag2_id={self.resdiag2_id}, resdiag2_desc={self.resdiag2_desc})>"


class ReferralFile(Base):
    """SQLAlchemy model for gs_referral_file table."""

    __tablename__ = "gs_referral_file"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    id = Column(Integer, primary_key=True)
    refer_id = Column(Integer, nullable=False)
    data_id = Column(Integer, nullable=False)

    def __repr__(self):
        return f"<ReferralFile(id={self.id}, refer_id={self.refer_id}, data_id={self.data_id})>"


class ReferralFileContents(Base):
    """SQLAlchemy model for gs_referral_filecontents table."""

    __tablename__ = "gs_referral_filecontents"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    id = Column(Integer, primary_key=True)
    ocr_data = Column(JSONB)
    created_date = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<ReferralFileContents(id={self.id})>"


class ReferralComment(Base):
    """SQLAlchemy model for gs_referral_comment table."""

    __tablename__ = "gs_referral_comment"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    id = Column(Integer, primary_key=True)
    patient_id = Column(Integer, nullable=False)
    refer_id = Column(Integer, nullable=False)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(Integer)
    created = Column(String)
    cmt_type = Column(String)
    subject = Column(String)
    status = Column(String)
    priority = Column(String)
    cmt = Column(Text)
    parent_id = Column(Integer, nullable=True)

    def __repr__(self):
        return f"<ReferralComment(id={self.id}, refer_id={self.refer_id})>"


class ReferralTask(Base):
    """SQLAlchemy model for gs_referral_task table."""

    __tablename__ = "gs_referral_task"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    id = Column(Integer, primary_key=True)
    cmt_id = Column(Integer, nullable=False)
    cmt_parent_id = Column(Integer, nullable=True)
    patient_id = Column(Integer, nullable=False)
    refer_id = Column(Integer, nullable=False)
    task_type = Column(String)
    subject = Column(String)
    status = Column(String)
    priority = Column(String)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(Integer)
    created = Column(String)
    created_task = Column(Text)
    modified_date = Column(DateTime(timezone=True), server_default=func.now())
    modified_by = Column(Integer)
    modified = Column(String)
    modified_task = Column(Text)

    def __repr__(self):
        return f"<ReferralTask(id={self.id}, refer_id={self.refer_id})>"


class PatientFacilityReferralFlag(Base):
    """SQLAlchemy model for gs_patient_facility_referral_flag table."""

    __tablename__ = "gs_patient_facility_referral_flag"
    __table_args__ = {"schema": None}  # Schema will be set dynamically

    refer_id = Column(Integer, primary_key=True)
    refer_dcsn_id = Column(Integer, primary_key=True)
    refer_dcsn_priority_json = Column(MutableDict.as_mutable(JSONB))
    ai_status = Column(String(255))
    ai_status_reason = Column(MutableDict.as_mutable(JSONB))
    ai_last_modified_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<PatientFacilityReferralFlag(id={self.id}, refer_id={self.refer_id})>"
