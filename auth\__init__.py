from fastapi import Depends, HTTPException, status, Request
from fastapi.security import (
    HTTPBasic,
    HTTPBasicCredentials,
    OAuth2PasswordRequestForm,
    OAuth2PasswordBearer,
)
from datetime import datetime, timedelta, timezone
import jwt
import bcrypt
from services.client_config_service import get_client_config_service
from utils.config_utils import get_auth_config, get_config_from_context
from exceptions import UnauthorizedError
import base64
import secrets
import logging


security = HTTPBasic()
# Use default configuration for OAuth2 scheme setup
_default_config = get_client_config_service().get_default_configuration()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{_default_config.root_path}/v1/token")


__valid_user_credentials = {}


async def __get_valid_user_credentials(request: Request = None):
    """
    Get the valid user credentials from the environment variable.
    """
    global __valid_user_credentials
    if __valid_user_credentials:
        return __valid_user_credentials

    # Get auth configuration (client-specific or default)
    if request:
        auth_config = get_auth_config(request)
        auth_credentials = auth_config.auth_credentials
        auth_keys_delimiter = auth_config.auth_keys_delimiter
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        auth_credentials = default_config.auth_credentials
        auth_keys_delimiter = default_config.auth_keys_delimiter

    __valid_user_credentials = {
        user_pass.split(":")[0]: user_pass.split(":")[1]
        for user_pass in auth_credentials.split(auth_keys_delimiter)
    }
    return __valid_user_credentials


async def verify_credentials(
    auth_form: OAuth2PasswordRequestForm, request: Request = None
):
    valid_user_credentials = await __get_valid_user_credentials(request)
    if auth_form.username in valid_user_credentials and bcrypt.checkpw(
        auth_form.password.encode(), valid_user_credentials[auth_form.username].encode()
    ):
        return

    raise UnauthorizedError("Invalid credentials!")


async def verify_user(username: str, request: Request = None):
    if not username:
        raise UnauthorizedError("Invalid user!")

    valid_user_credentials = await __get_valid_user_credentials(request)
    if username in valid_user_credentials:
        return

    raise UnauthorizedError("Invalid user!")


async def get_auth_token(
    data: dict,
    client_id: str = None,
    client_schema: str = None,
    request: Request = None,
):
    access_token = await create_access_token(data, client_id, client_schema, request)
    refresh_token = await create_refresh_token(data, client_id, client_schema, request)
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "refresh_token": refresh_token,
    }


async def verify_refresh_token(token: str, grand_type, request: Request = None) -> dict:
    if grand_type != "refresh_token":
        raise UnauthorizedError("Invalid grand type")

    # Get auth configuration (client-specific or default)
    if request:
        auth_config = get_auth_config(request)
        jwt_secret_key = auth_config.jwt_secret_key
        encryption_algorithm = auth_config.encryption_algorithm
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        jwt_secret_key = default_config.jwt_secret_key
        encryption_algorithm = default_config.encryption_algorithm

    try:
        payload = jwt.decode(token, jwt_secret_key, algorithms=[encryption_algorithm])
        if payload.get("type") != "refresh":
            raise UnauthorizedError("Invalid token type")
        return payload
    except jwt.ExpiredSignatureError:
        raise UnauthorizedError("Refresh token has expired")
    except jwt.PyJWTError:
        raise UnauthorizedError("Could not validate refresh token")


async def create_access_token(
    data: dict,
    client_id: str = None,
    client_schema: str = None,
    request: Request = None,
) -> str:
    to_encode = data.copy()

    # Get auth configuration (client-specific or default)
    if request:
        auth_config = get_auth_config(request)
        access_token_expire_minutes = auth_config.access_token_expire_minutes
        jwt_secret_key = auth_config.jwt_secret_key
        encryption_algorithm = auth_config.encryption_algorithm
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        access_token_expire_minutes = default_config.access_token_expire_minutes
        jwt_secret_key = default_config.jwt_secret_key
        encryption_algorithm = default_config.encryption_algorithm

    expire = datetime.now(timezone.utc) + timedelta(minutes=access_token_expire_minutes)
    to_encode.update({"exp": expire.timestamp(), "type": "access"})

    # Add client information to token if provided
    if client_id:
        to_encode["client_id"] = client_id
    if client_schema:
        to_encode["client_schema"] = client_schema

    return jwt.encode(to_encode, jwt_secret_key, algorithm=encryption_algorithm)


async def create_refresh_token(
    data: dict,
    client_id: str = None,
    client_schema: str = None,
    request: Request = None,
) -> str:
    to_encode = data.copy()

    # Get auth configuration (client-specific or default)
    if request:
        auth_config = get_auth_config(request)
        refresh_token_expire_minutes = auth_config.refresh_token_expire_minutes
        jwt_secret_key = auth_config.jwt_secret_key
        encryption_algorithm = auth_config.encryption_algorithm
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        refresh_token_expire_minutes = default_config.refresh_token_expire_minutes
        jwt_secret_key = default_config.jwt_secret_key
        encryption_algorithm = default_config.encryption_algorithm

    expire = datetime.now(timezone.utc) + timedelta(
        minutes=refresh_token_expire_minutes
    )
    to_encode.update({"exp": expire.timestamp(), "type": "refresh"})

    # Add client information to token if provided
    if client_id:
        to_encode["client_id"] = client_id
    if client_schema:
        to_encode["client_schema"] = client_schema

    return jwt.encode(to_encode, jwt_secret_key, algorithm=encryption_algorithm)


async def verify_token(token: str = Depends(oauth2_scheme), request: Request = None):
    # Get auth configuration (client-specific or default)
    if request:
        auth_config = get_auth_config(request)
        jwt_secret_key = auth_config.jwt_secret_key
        encryption_algorithm = auth_config.encryption_algorithm
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        jwt_secret_key = default_config.jwt_secret_key
        encryption_algorithm = default_config.encryption_algorithm

    try:
        payload = jwt.decode(token, jwt_secret_key, algorithms=[encryption_algorithm])
        token_type: str = payload.get("type")
        if token_type != "access":
            raise UnauthorizedError("Invalid token type: must be access token")

        username = payload.get("user")
        await verify_user(username, request)
        return username
    except jwt.ExpiredSignatureError:
        raise UnauthorizedError("Token has expired")
    except jwt.InvalidTokenError:
        raise UnauthorizedError("Invalid token")


def authenticate_swagger_calls(
    credentials: HTTPBasicCredentials = Depends(security), request: Request = None
):
    # Get auth configuration (client-specific or default)
    if request:
        auth_config = get_auth_config(request)
        swagger_basic_auth_key = auth_config.swagger_basic_auth_key
    else:
        # Fallback to default configuration
        default_config = get_client_config_service().get_default_configuration()
        swagger_basic_auth_key = default_config.swagger_basic_auth_key

    auth_value = f"{base64.b64encode(f'{credentials.username}:{credentials.password}'.encode()).decode()}"
    if not secrets.compare_digest(auth_value, swagger_basic_auth_key):
        logging.error(f"Not authenticated! {credentials.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated!",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username
